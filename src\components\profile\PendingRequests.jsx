import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Clock, ChevronUp, ChevronDown, X, Calendar } from 'lucide-react';
import { timeAgo, formatDate } from './utils/dateUtils';

// Helper function to capitalize strings
const capitalize = (str) => {
	if (!str) return '';
	return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

// Helper function to render data comparison
const renderDataComparison = (data, type) => {
	if (!data || typeof data !== 'object') {
		return (
			<div className="text-sm text-gray-600">
				{data || 'No data available'}
			</div>
		);
	}

	// Handle arrays (like children, education, experience)
	if (Array.isArray(data)) {
		if (data.length === 0) {
			return (
				<div className="text-sm text-gray-500 italic">
					No items
				</div>
			);
		}

		return (
			<div className="space-y-3">
				{data.map((item, index) => (
					<div key={index} className="bg-white rounded-md p-3 border">
						<div className="text-xs font-medium text-gray-500 mb-2">
							Item {index + 1}
						</div>
						{Object.entries(item).map(([key, value]) => (
							<div key={key} className="flex justify-between items-center py-1">
								<span className="text-xs text-gray-600 capitalize">
									{key.replace(/([A-Z])/g, ' $1').trim()}:
								</span>
								<span className="text-sm font-medium text-right max-w-[60%] truncate">
									{formatFieldValue(value)}
								</span>
							</div>
						))}
					</div>
				))}
			</div>
		);
	}

	// Handle objects
	return (
		<div className="space-y-2">
			{Object.entries(data).map(([key, value]) => (
				<div key={key} className="flex justify-between items-center py-2 border-b border-gray-200 last:border-b-0">
					<span className="text-sm text-gray-600 capitalize font-medium">
						{key.replace(/([A-Z])/g, ' $1').trim()}:
					</span>
					<span className={`text-sm text-right max-w-[60%] truncate ${
						type === 'requested' ? 'font-semibold text-blue-700' : 'text-gray-700'
					}`}>
						{formatFieldValue(value)}
					</span>
				</div>
			))}
		</div>
	);
};

// Helper function to format field values
const formatFieldValue = (value) => {
	if (value === null || value === undefined) return 'N/A';
	if (typeof value === 'boolean') return value ? 'Yes' : 'No';
	if (typeof value === 'string' && value.includes('T') && value.includes('Z')) {
		// Likely an ISO date string
		return formatDate(value);
	}
	if (Array.isArray(value)) return `${value.length} items`;
	if (typeof value === 'object') return 'Complex data';
	return String(value);
};

export function PendingRequests({ pendingChanges, onCancelRequest }) {
	const [showPendingRequests, setShowPendingRequests] = useState(false);

	if (pendingChanges.length === 0) {
		return null;
	}

	return (
		<Collapsible
			open={showPendingRequests}
			onOpenChange={setShowPendingRequests}
			className="mb-6 border rounded-lg overflow-hidden"
		>
			<div className="bg-amber-50 border-b border-amber-200">
				<CollapsibleTrigger className="flex items-center justify-between w-full p-4 text-left">
					<div className="flex items-center gap-2">
						<Clock className="h-5 w-5 text-amber-600" />
						<div>
							<h3 className="font-medium text-amber-800">
								Pending Change Requests
							</h3>
							<p className="text-sm text-amber-700">
								You have {pendingChanges.length} pending change request
								{pendingChanges.length > 1 ? 's' : ''}
							</p>
						</div>
					</div>
					<div className="flex items-center gap-2">
						<Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">
							{pendingChanges.length} Pending
						</Badge>
						{showPendingRequests ? (
							<ChevronUp className="h-5 w-5 text-amber-600" />
						) : (
							<ChevronDown className="h-5 w-5 text-amber-600" />
						)}
					</div>
				</CollapsibleTrigger>
			</div>
			<CollapsibleContent>
				<div className="p-6 bg-white">
					<div className="space-y-6">
						{pendingChanges.map((change) => (
							<div
								key={change._id}
								className="border rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-shadow"
							>
								{/* Header */}
								<div className="bg-gradient-to-r from-amber-50 to-orange-50 px-6 py-4 border-b">
									<div className="flex justify-between items-start">
										<div className="flex-1">
											<div className="flex items-center gap-3 mb-2">
												<Badge
													className={
														change.status === 'pending'
															? 'bg-amber-100 text-amber-800 border-amber-200'
															: change.status === 'approved'
																? 'bg-green-100 text-green-800 border-green-200'
																: 'bg-red-100 text-red-800 border-red-200'
													}
												>
													<Clock className="h-3 w-3 mr-1" />
													{change.status === 'pending'
														? 'Pending Approval'
														: change.status === 'approved'
															? 'Approved'
															: 'Rejected'}
												</Badge>
												<span className="text-sm text-muted-foreground">
													Submitted {timeAgo(change.submittedAt)}
												</span>
											</div>
											<h3 className="text-lg font-semibold text-gray-900">
												{capitalize(change.section.replace('-', ' '))} Information Update
											</h3>
											<p className="text-sm text-muted-foreground mt-1">
												Request ID: {change._id.slice(-8)}
											</p>
										</div>
										{change.status === 'pending' && (
											<Button
												variant="ghost"
												size="sm"
												className="text-red-600 hover:text-red-700 hover:bg-red-50"
												onClick={() => onCancelRequest(change._id)}
											>
												<X className="h-4 w-4 mr-1" />
												Cancel
											</Button>
										)}
									</div>
								</div>

								{/* Data Comparison */}
								<div className="p-6">
									<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
										{/* Current Data */}
										<div className="space-y-4">
											<div className="flex items-center gap-2 mb-3">
												<div className="w-3 h-3 bg-gray-400 rounded-full"></div>
												<h4 className="font-medium text-gray-700">Current Information</h4>
											</div>
											<div className="bg-gray-50 rounded-lg p-4 border-l-4 border-gray-400">
												{renderDataComparison(change.oldData, 'current')}
											</div>
										</div>

										{/* Requested Data */}
										<div className="space-y-4">
											<div className="flex items-center gap-2 mb-3">
												<div className="w-3 h-3 bg-blue-500 rounded-full"></div>
												<h4 className="font-medium text-blue-700">Requested Changes</h4>
											</div>
											<div className="bg-blue-50 rounded-lg p-4 border-l-4 border-blue-500">
												{renderDataComparison(change.newData, 'requested')}
											</div>
										</div>
									</div>

									{/* Reason */}
									{change.reason && (
										<div className="mt-6 pt-4 border-t">
											<div className="flex items-start gap-2">
												<div className="w-5 h-5 bg-amber-100 rounded-full flex items-center justify-center mt-0.5">
													<span className="text-amber-600 text-xs font-bold">!</span>
												</div>
												<div className="flex-1">
													<h5 className="font-medium text-gray-700 mb-1">Reason for Change</h5>
													<p className="text-sm text-gray-600 leading-relaxed">
														{change.reason}
													</p>
												</div>
											</div>
										</div>
									)}

									{/* Status Footer */}
									<div className="mt-6 pt-4 border-t bg-gray-50 -mx-6 -mb-6 px-6 py-4">
										<div className="flex items-center justify-between">
											<div className="flex items-center gap-2 text-sm text-muted-foreground">
												<Calendar className="h-4 w-4" />
												<span>Submitted on {formatDate(change.submittedAt)}</span>
											</div>
											{change.status === 'pending' && (
												<div className="flex items-center gap-2 text-sm text-amber-600">
													<Clock className="h-4 w-4" />
													<span>Awaiting manager approval</span>
												</div>
											)}
										</div>
									</div>
								</div>
							</div>
						))}
					</div>
				</div>
			</CollapsibleContent>
		</Collapsible>
	);
}
