import { useState } from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Clock, ChevronUp, ChevronDown, X } from 'lucide-react';
import { timeAgo, formatDate } from './utils/dateUtils';

// Helper function to capitalize strings
const capitalize = (str) => {
	if (!str) return '';
	return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

// Helper function to format data for display
const formatDataForDisplay = (data) => {
	if (!data) return 'N/A';
	if (typeof data === 'string') return data;
	if (typeof data === 'object') {
		// Handle arrays
		if (Array.isArray(data)) {
			return data.length > 0 ? `${data.length} items` : 'No items';
		}
		// Handle objects - show key-value pairs
		const entries = Object.entries(data);
		if (entries.length === 0) return 'No data';
		if (entries.length === 1) {
			const [key, value] = entries[0];
			return `${key}: ${value}`;
		}
		return `${entries.length} fields updated`;
	}
	return String(data);
};

export function PendingRequests({ pendingChanges, onCancelRequest }) {
	const [showPendingRequests, setShowPendingRequests] = useState(false);

	if (pendingChanges.length === 0) {
		return null;
	}

	return (
		<Collapsible
			open={showPendingRequests}
			onOpenChange={setShowPendingRequests}
			className="mb-6 border rounded-lg overflow-hidden"
		>
			<div className="bg-amber-50 border-b border-amber-200">
				<CollapsibleTrigger className="flex items-center justify-between w-full p-4 text-left">
					<div className="flex items-center gap-2">
						<Clock className="h-5 w-5 text-amber-600" />
						<div>
							<h3 className="font-medium text-amber-800">
								Pending Change Requests
							</h3>
							<p className="text-sm text-amber-700">
								You have {pendingChanges.length} pending change request
								{pendingChanges.length > 1 ? 's' : ''}
							</p>
						</div>
					</div>
					<div className="flex items-center gap-2">
						<Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">
							{pendingChanges.length} Pending
						</Badge>
						{showPendingRequests ? (
							<ChevronUp className="h-5 w-5 text-amber-600" />
						) : (
							<ChevronDown className="h-5 w-5 text-amber-600" />
						)}
					</div>
				</CollapsibleTrigger>
			</div>
			<CollapsibleContent>
				<div className="p-4 bg-white">
					<div className="space-y-4">
						{pendingChanges.map((change) => (
							<div
								key={change._id}
								className="border rounded-md p-4 bg-slate-50"
							>
								<div className="flex justify-between items-start">
									<div>
										<div className="flex items-center gap-2">
											<Badge
												className={
													change.status === 'pending'
														? 'bg-amber-100 text-amber-800'
														: change.status === 'approved'
															? 'bg-green-100 text-green-800'
															: 'bg-red-100 text-red-800'
												}
											>
												{change.status === 'pending'
													? 'Pending Approval'
													: change.status === 'approved'
														? 'Approved'
														: 'Rejected'}
											</Badge>
											<span className="text-sm text-muted-foreground">
												{timeAgo(change.submittedAt)}
											</span>
										</div>
										<h4 className="font-medium mt-2">
											{capitalize(change.section.replace('-', ' '))} -{' '}
											{change.field || 'Field Update'}
										</h4>
										<div className="mt-1 grid grid-cols-2 gap-4">
											<div>
												<p className="text-xs text-muted-foreground">
													Current Value
												</p>
												<p className="text-sm">
													{formatDataForDisplay(change.oldData)}
												</p>
											</div>
											<div>
												<p className="text-xs text-muted-foreground">
													Requested Value
												</p>
												<p className="text-sm font-medium">
													{formatDataForDisplay(change.newData)}
												</p>
											</div>
										</div>
										{change.reason && (
											<div className="mt-2">
												<p className="text-xs text-muted-foreground">
													Reason
												</p>
												<p className="text-sm">{change.reason}</p>
											</div>
										)}
									</div>
									{change.status === 'pending' && (
										<Button
											variant="ghost"
											size="icon"
											className="h-8 w-8"
											onClick={() => onCancelRequest(change._id)}
										>
											<X className="h-4 w-4" />
										</Button>
									)}
								</div>
							</div>
						))}
					</div>
				</div>
			</CollapsibleContent>
		</Collapsible>
	);
}
