import React, { useEffect, useState } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
	Baby,
	Calendar,
	Check,
	ChevronsUpDown,
	Edit,
	Loader2,
	Plus,
	Save,
	Trash2,
	X,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { calculateAge, cn, formatDate } from '@/lib/utils';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateEmployeeDetailsPersonal } from '@/lib/features/employees/updateEmployeeSlice';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from './ui/select';
import { z } from 'zod';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from './ui/command';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { fetchCountries } from '@/lib/features/location/locationSlice';
import {
	childrenSchema,
	maritalSchema,
} from '@/lib/schemas/employeeRegistrationSchema';

const familyDetailsSchema = z.object({
	...maritalSchema.shape,
	...childrenSchema.shape,
});

const EmployeeDetailsFamilyForm = ({ employeeId, familyDetails }) => {
	const { isLoading } = useAppSelector((store) => store.employee);
	const { countries, isLoading: isLoadingCountries } = useAppSelector(
		(store) => store.location
	);
	const dispatch = useAppDispatch();
	const [isEditing, setIsEditing] = useState(false);

	// Extract family details
	const {
		maritalStatus = 'single',
		spouseName = '',
		spouseEmploymentStatus = 'employed',
		children = [],
	} = familyDetails;

	const form = useForm({
		resolver: zodResolver(familyDetailsSchema),
		defaultValues: {
			maritalStatus,
			spouseName,
			spouseEmploymentStatus,
			children: children.map((child) => ({
				...child,
				dob: child.dob ? child.dob.split('T')[0] : '',
				age: child.age || calculateAge(child.dob),
			})),
		},
	});

	const {
		fields: childrenFields,
		append: appendChild,
		remove: removeChild,
	} = useFieldArray({
		control: form.control,
		name: 'children',
	});

	const watchMaritalStatus = form.watch('maritalStatus');

	useEffect(() => {
		dispatch(fetchCountries());
	}, [dispatch]);

	const onSubmit = async (data) => {
		console.log('Form data to submit:', data);

		// Format the data for API
		const formattedData = {
			maritalStatus: data.maritalStatus,
			spouseName: data.maritalStatus === 'married' ? data.spouseName : '',
			spouseEmploymentStatus:
				data.maritalStatus === 'married' ? data.spouseEmploymentStatus : '',
			children: data.children.map((child) => ({
				...child,
				dob: new Date(child.dob).toISOString(),
				age: calculateAge(child.dob),
			})),
			employeeId,
		};

		// Call the API to update family details
		const result = await dispatch(
			updateEmployeeDetailsPersonal({
				employeeId,
				...formattedData,
			})
		);

		// Check if update was successful
		if (updateEmployeeDetailsPersonal.fulfilled.match(result)) {
			setIsEditing(false);
		}
	};

	const addNewChild = () => {
		appendChild({
			name: '',
			dob: '',
			nationality: '',
			age: 0,
		});
	};

	return (
		<>
			{/* Edit Controls */}
			<div className="flex justify-end mb-4">
				<div className="flex gap-2">
					{isEditing && (
						<Button
							className="bg-red-600 text-white"
							variant="outline"
							onClick={() => setIsEditing(false)}
							disabled={isLoading}
						>
							{<X className="h-4 w-4 mr-2" size={16} />}Cancel
						</Button>
					)}
					<Button
						variant="default"
						onClick={() =>
							isEditing ? form.handleSubmit(onSubmit)() : setIsEditing(true)
						}
						disabled={isLoading}
					>
						{isLoading ? (
							<Loader2 className="animate-spin mr-2" size={16} />
						) : isEditing ? (
							<Save className="h-4 w-4 mr-2" size={16} />
						) : (
							<Edit className="h-4 w-4 mr-2" size={16} />
						)}
						{isEditing ? 'Save Changes' : 'Edit Family Details'}
					</Button>
				</div>
			</div>

			<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
				{/* Marital Information Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle>Marital Information</CardTitle>
					</CardHeader>
					<CardContent>
						{isEditing ? (
							<Form {...form}>
								<div className="grid grid-cols-1 gap-4">
									<FormField
										control={form.control}
										name="maritalStatus"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium text-muted-foreground">
													Marital Status
												</FormLabel>
												<Select
													onValueChange={field.onChange}
													defaultValue={field.value}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Select marital status" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="single">Single</SelectItem>
														<SelectItem value="married">Married</SelectItem>
														<SelectItem value="other">Other</SelectItem>
														<SelectItem value="prefer-not-to-say">
															Prefer not to say
														</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>

									{watchMaritalStatus === 'married' && (
										<>
											<FormField
												control={form.control}
												name="spouseName"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-sm font-medium text-muted-foreground">
															Spouse Name
														</FormLabel>
														<FormControl>
															<Input
																placeholder="Enter spouse name"
																{...field}
															/>
														</FormControl>
														<FormMessage />
													</FormItem>
												)}
											/>
											<FormField
												control={form.control}
												name="spouseEmploymentStatus"
												render={({ field }) => (
													<FormItem>
														<FormLabel className="text-sm font-medium text-muted-foreground">
															Spouse Employment Status
														</FormLabel>
														<Select
															onValueChange={field.onChange}
															defaultValue={field.value}
														>
															<FormControl>
																<SelectTrigger>
																	<SelectValue placeholder="Select employment status" />
																</SelectTrigger>
															</FormControl>
															<SelectContent>
																<SelectItem value="employed">
																	Employed
																</SelectItem>
																<SelectItem value="unemployed">
																	Unemployed
																</SelectItem>
																<SelectItem value="self-employed">
																	Self-employed
																</SelectItem>
																<SelectItem value="retired">Retired</SelectItem>
																<SelectItem value="student">Student</SelectItem>
															</SelectContent>
														</Select>
														<FormMessage />
													</FormItem>
												)}
											/>
										</>
									)}
								</div>
							</Form>
						) : (
							<div className="grid grid-cols-2 gap-4">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										Marital Status
									</p>
									<p className="capitalize">{maritalStatus}</p>
								</div>
								{maritalStatus === 'married' && (
									<>
										<div>
											<p className="text-sm font-medium text-muted-foreground">
												Spouse Name
											</p>
											<p>{spouseName}</p>
										</div>
										<div>
											<p className="text-sm font-medium text-muted-foreground">
												Spouse Employment
											</p>
											<p className="capitalize">{spouseEmploymentStatus}</p>
										</div>
									</>
								)}
							</div>
						)}
					</CardContent>
				</Card>

				{/* Children Card */}
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle>Children</CardTitle>
						{isEditing && (
							<Button
								type="button"
								variant="outline"
								size="sm"
								onClick={addNewChild}
							>
								<Plus className="h-4 w-4 mr-2" />
								Add Child
							</Button>
						)}
					</CardHeader>
					<CardContent>
						{isEditing ? (
							<Form {...form}>
								{childrenFields.length > 0 ? (
									<div className="space-y-6">
										{childrenFields.map((field, index) => (
											<div
												key={field.id}
												className="space-y-4 p-4 border rounded-md relative"
											>
												<Button
													type="button"
													variant="ghost"
													size="icon"
													className="absolute top-2 right-2 h-6 w-6 text-destructive"
													onClick={() => removeChild(index)}
												>
													<Trash2 className="h-4 w-4" />
												</Button>

												<FormField
													control={form.control}
													name={`children.${index}.name`}
													render={({ field }) => (
														<FormItem>
															<FormLabel className="text-sm font-medium text-muted-foreground">
																Child&apos;s Name
															</FormLabel>
															<FormControl>
																<Input
																	placeholder="Enter child's name"
																	{...field}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												<div className="grid grid-cols-2 gap-4">
													<FormField
														control={form.control}
														name={`children.${index}.dob`}
														render={({ field }) => (
															<FormItem>
																<FormLabel className="text-sm font-medium text-muted-foreground">
																	Date of Birth
																</FormLabel>
																<FormControl>
																	<Input type="date" {...field} />
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>

													<FormField
														control={form.control}
														name={`children.${index}.nationality`}
														render={({ field }) => (
															<FormItem>
																<FormLabel className="text-sm font-medium text-muted-foreground">
																	Nationality
																</FormLabel>
																<Popover>
																	<PopoverTrigger asChild>
																		<FormControl>
																			<Button
																				variant="outline"
																				role="combobox"
																				className={cn(
																					'w-full justify-between',
																					!field.value &&
																						'text-muted-foreground'
																				)}
																			>
																				{field.value
																					? countries.find(
																							(country) =>
																								country.name === field.value
																						)?.name
																					: 'Select country'}
																				{isLoadingCountries && (
																					<Loader2 className="h-4 w-4 animate-spin" />
																				)}
																				{!isLoadingCountries && (
																					<ChevronsUpDown className="opacity-50" />
																				)}
																			</Button>
																		</FormControl>
																	</PopoverTrigger>
																	<PopoverContent className="w-full p-0">
																		<Command>
																			<CommandInput placeholder="Search Country..." />
																			<CommandList className="w-full">
																				<CommandEmpty>
																					No Country found.
																				</CommandEmpty>
																				<CommandGroup>
																					{countries.map((country) => (
																						<CommandItem
																							key={country._id}
																							value={country.name}
																							onSelect={() => {
																								form.setValue(
																									`children.${index}.nationality`,
																									country.name
																								);
																							}}
																						>
																							<Check
																								className={cn(
																									'mr-2 h-4 w-4',
																									field.value === country.name
																										? 'opacity-100'
																										: 'opacity-0'
																								)}
																							/>
																							{country.name}
																						</CommandItem>
																					))}
																				</CommandGroup>
																			</CommandList>
																		</Command>
																	</PopoverContent>
																</Popover>
																<FormMessage />
															</FormItem>
														)}
													/>
												</div>
											</div>
										))}
									</div>
								) : (
									<div className="flex flex-col items-center justify-center py-6 text-center">
										<Baby className="h-8 w-8 text-muted-foreground mb-2" />
										<p className="text-muted-foreground">
											No children added yet
										</p>
										<Button
											type="button"
											variant="outline"
											size="sm"
											className="mt-4"
											onClick={addNewChild}
										>
											<Plus className="h-4 w-4 mr-2" />
											Add Child
										</Button>
									</div>
								)}
							</Form>
						) : (
							<>
								{children && children.length > 0 ? (
									<div className="space-y-4">
										{children.map((child, index) => (
											<div key={child._id || index} className="mb-4 last:mb-0">
												<div className="flex justify-between items-center mb-2">
													<h4 className="font-medium">{child.name}</h4>
													<Badge variant="outline">{child.age} years</Badge>
												</div>
												<p className="text-sm text-muted-foreground">
													Born on {formatDate(child.dob)}
												</p>
												<p className="text-sm text-muted-foreground">
													Nationality: {child.nationality}
												</p>
											</div>
										))}
									</div>
								) : (
									<div className="flex flex-col items-center justify-center py-6 text-center">
										<Baby className="h-8 w-8 text-muted-foreground mb-2" />
										<p className="text-muted-foreground">
											No children information available
										</p>
									</div>
								)}
							</>
						)}
					</CardContent>
				</Card>
			</div>
		</>
	);
};

export default EmployeeDetailsFamilyForm;
