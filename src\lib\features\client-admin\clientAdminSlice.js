import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { customFetch, showErrors } from '@/lib/utils';
import { toast } from 'sonner';
import { showUser } from '../auth/authSlice';

const initialState = {
	companyData: {},
	logo: null,
	businessDetails: null,
	currencyAndTimeDetails: null,
	branches: [],
	steps: 0,
	isLoading: false,
	onboardingSuccess: false,
};

export const fetchFileFromBlobURL = async (blobURL, name) => {
	if (blobURL instanceof File) {
		return blobURL; // Already a file, no need to fetch
	}
	try {
		const response = await fetch(blobURL);
		const blob = await response.blob();
		return new File([blob], `${name}.png`, { type: blob.type });
	} catch (error) {
		console.error('Failed to convert Blob URL to File', error);
		return null;
	}
};

export const onboardingStepOne = createAsyncThunk(
	'clientAdmin/onboardingStepOne',
	async (businessDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/client-admin/onboard',
				businessDetails,
				{
					headers: {
						'Content-Type': 'multipart/form-data',
					},
				}
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const onboardingFinalStep = createAsyncThunk(
	'clientAdmin/onboardingFinalStep',
	async (businessDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/client-admin/onboard',
				businessDetails
			);
			if (data?.success) {
				thunkAPI.dispatch(showUser());
			}
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const clientAdminSlice = createSlice({
	name: 'clientAdmin',
	initialState,
	reducers: {
		increaseSteps: (state) => {
			state.steps = state.steps >= 5 ? 5 : state.steps + 1;
		},
		decreaseSteps: (state) => {
			state.steps = state.steps <= 0 ? 0 : state.steps - 1;
		},
		collectData: (state, { payload }) => {
			state.businessDetails = payload;
		},
		setSteps: (state, { payload }) => {
			state.steps = payload;
		},

		setLogo: (state, { payload }) => {
			console.log(` payload:`, payload);
			state.logo = payload;
		},

		updateForm: (state, { payload }) => {
			state.businessDetails = { ...state.businessDetails, ...payload };
		},

		updateCurrencyAndTime: (state, { payload }) => {
			state.currencyAndTimeDetails = {
				...state.currencyAndTimeDetails,
				...payload,
			};
		},

		setCurrency: (state, { payload }) => {
			state.currencyAndTimeDetails = {
				currency: payload,
			};
		},
		setCountryCode: (state, action) => {
			state.companyData.businessCountryCode = action.payload;
		},
    updateBranches: (state, action) => {
			state.branches = action.payload.branches.map((branch, index) => ({
				branchId: branch.branchId || `branch-${index}`,
				...branch,
			}));
		},
		updateDepartments(state, action) {
			const { departments } = action.payload;
			state.branches = state.branches.map((branch) => ({
				...branch,
				departments: departments[branch.branchId] || [],
			}));
		},
	},
	extraReducers: (builder) => {
		builder
			.addCase(onboardingStepOne.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(onboardingStepOne.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.steps = 2;
				toast.success('Business Details Submitted Successfully');
			})
			.addCase(onboardingStepOne.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(onboardingFinalStep.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(onboardingFinalStep.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.onboardingSuccess = true;
				toast.success('Welcome to the family!');
			})
			.addCase(onboardingFinalStep.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

export const {
	increaseSteps,
	decreaseSteps,
	collectData,
	setLogo,
	setCurrency,
	setCountryCode,
	setSteps,
	updateForm,
	updateCurrencyAndTime,
  updateBranches,
  updateDepartments,
} = clientAdminSlice.actions;

export default clientAdminSlice.reducer;
