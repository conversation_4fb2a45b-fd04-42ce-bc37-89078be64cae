import { showErrors } from '@/lib/utils';
import { toast } from 'sonner';
import { fetchEmployeeDetails } from './employeeSlice';

const { createSlice, createAsyncThunk } = require('@reduxjs/toolkit');

const initialState = {
	isLoading: false,
};

export const updateEmployeeDetailsPersonal = createAsyncThunk(
	'employeeUpdateDetails/updateEmployeeDetailsPersonal',
	async (employeeDetailsPersonalData, thunkAPI) => {
		try {
			console.log(employeeDetailsPersonalData);

			const data = await new Promise((resolve) => {
				setTimeout(() => {
					resolve({
						success: true,
						message: 'Personal information updated successfully',
					});
				}, 3000);
			});

			if (data?.success) {
				thunkAPI.dispatch(
					fetchEmployeeDetails(employeeDetailsPersonalData.employeeId)
				);
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const employeeUpdateDetailsSlice = createSlice({
	name: 'employeeUpdateDetails',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(updateEmployeeDetailsPersonal.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Personal Information...');
			})
			.addCase(
				updateEmployeeDetailsPersonal.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					toast.success(payload.message);
				}
			)
			.addCase(updateEmployeeDetailsPersonal.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

export const {} = employeeUpdateDetailsSlice.actions;

export default employeeUpdateDetailsSlice.reducer;
