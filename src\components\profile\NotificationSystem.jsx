import { Alert, AlertDescription } from '@/components/ui/alert';
import { CheckCircle2, AlertCircle } from 'lucide-react';

export function NotificationSystem({ notifications }) {
	return (
		<div className="fixed top-4 right-4 z-50 space-y-2 max-w-md">
			{notifications.map((notification) => (
				<Alert
					key={notification.id}
					className={`border-${
						notification.type === 'success'
							? 'green'
							: notification.type === 'error'
								? 'red'
								: 'amber'
					}-500 bg-${
						notification.type === 'success'
							? 'green'
							: notification.type === 'error'
								? 'red'
								: 'amber'
					}-50 shadow-md transition-all duration-300 ease-in-out`}
				>
					{notification.type === 'success' ? (
						<CheckCircle2 className="h-5 w-5 text-green-600" />
					) : notification.type === 'error' ? (
						<AlertCircle className="h-5 w-5 text-red-600" />
					) : (
						<AlertCircle className="h-5 w-5 text-amber-600" />
					)}
					<AlertDescription
						className={`text-${
							notification.type === 'success'
								? 'green'
								: notification.type === 'error'
									? 'red'
									: 'amber'
						}-700`}
					>
						{notification.message}
					</AlertDescription>
				</Alert>
			))}
		</div>
	);
}
