import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

const NotificationCard = ({
	notifications = [
		{
			id: 1,
			fromName: '<PERSON><PERSON><PERSON> (Project Manager)',
			time: '5 mins ago',
			message:
				'Hey, could you go through the updated sprint backlog and ensure your tasks are in sync before the stand-up?',
		},
		{
			id: 2,
			fromName: '<PERSON><PERSON> (DevOps Engineer)',
			time: '30 mins ago',
			message:
				'The latest build was deployed to staging successfully. Please run a quick sanity check when you get a chance.',
		},
		{
			id: 3,
			fromName: '<PERSON><PERSON><PERSON> (Backend Developer)',
			time: '1 hour ago',
			message:
				'Just pushed the latest changes for the auth flow refactor. Ping me if anything looks off or needs tweaking.',
		},
	],
}) => {
	return (
		<div className="flex-1 bg-white shadow-sm border border-gray-200 rounded-xl p-3 space-y-3 text-sm min-h-[200px]">
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
				<h2 className="text-base font-semibold text-gray-800 flex items-center gap-2 h-8">
					Notifications
				</h2>
			</div>

			<div className="max-h-[140px] overflow-y-auto space-y-2 border-t pt-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
				{notifications.map((note) => (
					<Alert className="pl-3 border-0 py-1 w-full" key={note.id}>
						<AlertTitle className="font-semibold">
							{note.fromName}
							<span className="text-gray-500 text-xs ml-2">{note.time}</span>
						</AlertTitle>
						<AlertDescription className="flex justify-between items-center">
							<span className="pr-2">{note.message}</span>
							<Button size="sm" variant="outline" className="text-xs px-2 py-1">
								Reply
							</Button>
						</AlertDescription>
					</Alert>
				))}
			</div>
		</div>
	);
};

export default NotificationCard;
