'use client';
import { useEffect, useState, useCallback, useMemo } from 'react';
import { debounce } from 'lodash'; // Import Lodash debounce
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { branchesSchema } from '@/lib/schemas/onboardingSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm } from 'react-hook-form';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Check, ChevronsUpDown, Loader2, TrashIcon } from 'lucide-react';
import {
	updateBranches,
	setSteps,
} from '@/lib/features/client-admin/clientAdminSlice';
import { fetchCities } from '@/lib/features/location/locationSlice';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from './ui/form';
import {
	Command,
	CommandEmpty,
	CommandGroup,
	CommandInput,
	CommandItem,
	CommandList,
} from './ui/command';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { cn } from '@/lib/utils';

export function OnboardingBranchForm() {
	const { branches, businessDetails } = useAppSelector(
		(store) => store.clientAdmin
	);
  const countryId = businessDetails.selectedCountryId || businessDetails.businessCountry._id
  const { cities, isLoadingCities } = useAppSelector((store) => store.location)
	const dispatch = useAppDispatch();

	const form = useForm({
		resolver: zodResolver(branchesSchema),
		defaultValues: {
			branches: branches.map((branch) => ({
				branchName: branch.branchName || '',
				branchLocation: branch.branchLocation || '',
			})),
		},
	});

	const { fields, append, remove } = useFieldArray({
		control: form.control,
		name: 'branches',
	});

	const [searchTerm, setSearchTerm] = useState('');

	// Debounce function for API call
	const debouncedFetchCities = useMemo(
		() =>
			debounce((search) => {
				dispatch(
					fetchCities({
						countryId: countryId,
						citySearchTerm: search,
					})
				);
			}, 500),
		[countryId, dispatch]
	);

	// Trigger debounced function on input change
	const handleSearchChange = (value) => {
		setSearchTerm(value);
		debouncedFetchCities(value);
	};

	useEffect(() => {
		return () => {
			debouncedFetchCities.cancel();
		};
	}, [debouncedFetchCities]);

	useEffect(() => {
		if (countryId) {
			dispatch(
				fetchCities({
					countryId: Number(countryId),
					citySearchTerm: '',
				})
			);
		}
	}, [dispatch, countryId]);

	useEffect(() => {
		const subscription = form.watch((value) => {
			const clonedBranches = value.branches.map((branch) => ({ ...branch }));
			dispatch(updateBranches({ branches: clonedBranches }));
		});
		return () => subscription.unsubscribe();
	}, [dispatch, form.watch, form]);

	const onSubmit = (data) => {
		dispatch(setSteps(4));
	};

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				className="flex flex-col gap-4 w-full min-h-80 items-center justify-between"
			>
				<div className="grid gap-4">
					{fields.map((branch, index) => (
						<div key={branch.id} className="grid grid-cols-12 gap-2 w-full">
							{/* Branch Name Input */}
							<FormField
								control={form.control}
								name={`branches.${index}.branchName`}
								render={({ field }) => (
									<FormItem className="col-span-7">
										<FormLabel>Branch Name</FormLabel>
										<FormControl>
											<Input placeholder="Enter branch name" {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Branch Location Select with Debounced Search */}
							<FormField
								control={form.control}
								name={`branches.${index}.branchLocation`}
								render={({ field }) => (
									<FormItem className="col-span-4">
										<FormLabel>Branch Location</FormLabel>
										<Popover>
											<PopoverTrigger asChild>
												<FormControl>
													<Button
														variant="outline"
														role="combobox"
														className={cn(
															'w-full justify-between',
															!field.value && 'text-muted-foreground'
														)}
													>
														{field.value
															? cities.find(({ city }) => city === field.value)
																	?.city || field.value
															: 'Select city'}
														{isLoadingCities && (
															<Loader2 className="h-4 w-4 animate-spin" />
														)}
														{!isLoadingCities && (
															<ChevronsUpDown className="opacity-50" />
														)}
													</Button>
												</FormControl>
											</PopoverTrigger>
											<PopoverContent className="w-full p-0">
												<Command>
													{/* Search Input with Debounce */}
													<CommandInput
														placeholder="Search City..."
														className="h-9"
														value={searchTerm}
														onValueChange={handleSearchChange}
													/>
													<CommandList>
														{cities.length === 0 ? (
															<CommandEmpty>No City found.</CommandEmpty>
														) : (
															cities.map(({ city, state }, idx) => (
																<CommandItem
																	key={city + idx}
																	value={city}
																	onSelect={() =>
																		form.setValue(
																			`branches.${index}.branchLocation`,
																			city
																		)
																	}
																>
																	{`${city} (${state})`}
																	<Check
																		className={cn(
																			'ml-auto',
																			city === field.value
																				? 'opacity-100'
																				: 'opacity-0'
																		)}
																	/>
																</CommandItem>
															))
														)}
													</CommandList>
												</Command>
											</PopoverContent>
										</Popover>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Remove Branch Button */}
							{fields.length > 0 && (
								<div className="flex items-end col-span-1">
									<Button
										variant="outline"
										type="button"
										onClick={() => remove(index)}
										size={'icon'}
									>
										<TrashIcon />
									</Button>
								</div>
							)}
						</div>
					))}
					{/* Add Branch Button */}
					{fields.length < 3 && (
						<Button
							type="button"
							variant="outline"
							onClick={() => append({ branchName: '', branchLocation: '' })}
						>
							Add Branch
						</Button>
					)}
				</div>

				{/* Submit Button - Always at the Bottom */}
				<Button className="mt-auto w-full">Submit</Button>
			</form>
		</Form>
	);
}
