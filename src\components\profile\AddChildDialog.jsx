import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle, Baby } from 'lucide-react';
import { convertDateInputToIso } from './utils/dateUtils';

export function AddChildDialog({ open, onSubmit, onClose }) {
	// Helper function to convert YYYY-MM-DD format to ISO string for payload
	const convertDateInputToIso = (dateInput) => {
		if (!dateInput) return '';
		try {
			// Create date object from YYYY-MM-DD string
			const date = new Date(dateInput + 'T00:00:00.000Z');
			if (isNaN(date.getTime())) return '';
			
			// Return ISO string
			return date.toISOString();
		} catch (error) {
			console.error('Error converting date input to ISO:', error);
			return '';
		}
	};

	// Create a simple child validation schema
	const schema = z.object({
		name: z.string().min(1, 'Child name is required'),
		dob: z.string().min(1, 'Date of birth is required'),
		gender: z.enum(['male', 'female'], {
			required_error: 'Please select a gender',
		}),
		nationality: z.string().min(1, 'Nationality is required'),
		birthCertificate: z.string().optional(),
		reason: z.string().min(1, 'Reason for adding child is required'),
	});

	// Initialize form
	const form = useForm({
		resolver: zodResolver(schema),
		defaultValues: {
			name: '',
			dob: '',
			gender: '',
			nationality: '',
			birthCertificate: '',
			reason: '',
		},
	});

	// Reset form when dialog opens
	useEffect(() => {
		if (open) {
			form.reset({
				name: '',
				dob: '',
				gender: '',
				nationality: '',
				birthCertificate: '',
				reason: '',
			});
		}
	}, [open, form]);

	// Handle form submission
	const handleFormSubmit = (data) => {
		const { reason, ...childData } = data;
		
		// Convert DOB from YYYY-MM-DD to ISO format for payload
		if (childData.dob) {
			childData.dob = convertDateInputToIso(childData.dob);
		}
		
		// Submit the request
		onSubmit(childData, reason);
		form.reset();
	};

	if (!open) return null;

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className="max-w-3xl w-[95vw] max-h-[90vh] overflow-hidden flex flex-col">
				<DialogHeader className="flex-shrink-0 pb-4 border-b">
					<DialogTitle className="text-xl font-semibold flex items-center gap-2">
						<Baby className="h-5 w-5 text-blue-500" />
						Add Child Information
					</DialogTitle>
					<DialogDescription className="text-sm text-muted-foreground">
						Request to add a new child to your family information. All additions
						require approval from your manager.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(handleFormSubmit)}
						className="flex-1 overflow-y-auto py-4"
					>
						<div className="space-y-6">
							<Alert className="border-amber-200 bg-amber-50">
								<AlertCircle className="h-4 w-4 text-amber-600" />
								<AlertTitle className="text-amber-800">
									Approval Required
								</AlertTitle>
								<AlertDescription className="text-amber-700">
									Child information will be reviewed by your reporting manager
									before being added to your profile.
								</AlertDescription>
							</Alert>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<FormField
									control={form.control}
									name="name"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Child's Full Name *</FormLabel>
											<FormControl>
												<Input placeholder="Enter child's full name" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="dob"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Date of Birth *</FormLabel>
											<FormControl>
												<Input type="date" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="gender"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Gender *</FormLabel>
											<Select onValueChange={field.onChange} value={field.value}>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select gender" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													<SelectItem value="male">Male</SelectItem>
													<SelectItem value="female">Female</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="nationality"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Nationality *</FormLabel>
											<FormControl>
												<Input placeholder="Enter nationality" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="birthCertificate"
									render={({ field }) => (
										<FormItem className="md:col-span-2">
											<FormLabel>Birth Certificate Number</FormLabel>
											<FormControl>
												<Input placeholder="Enter birth certificate number (optional)" {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="reason"
									render={({ field }) => (
										<FormItem className="md:col-span-2">
											<FormLabel>Reason for Adding Child *</FormLabel>
											<FormControl>
												<Textarea
													placeholder="Please provide a reason for adding this child to your family information..."
													className="min-h-[100px]"
													{...field}
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</div>

						<DialogFooter className="flex-shrink-0 pt-6 border-t mt-6">
							<Button type="button" variant="outline" onClick={onClose}>
								Cancel
							</Button>
							<Button type="submit">Submit Request</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
