import { z } from 'zod';
import { religions } from '@/data/religions';

// Helper function to get religion values
const religionValues = religions.map(r => r.value);

// Base schema for common fields
const baseEditRequestSchema = z.object({
	reason: z
		.string()
		.min(10, 'Reason must be at least 10 characters')
		.max(500, 'Reason must not exceed 500 characters')
		.nonempty('Reason is required'),
});

// Personal details schema
export const personalDetailsEditSchema = baseEditRequestSchema.extend({
	nameOnNRIC: z
		.string()
		.min(3, 'Full Name must be at least 3 characters')
		.max(50, 'Full Name must not exceed 50 characters')
		.regex(/^[a-zA-Z\s]+$/, 'Full Name must contain only letters and spaces')
		.optional(),

	dob: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format')
		.optional(),

	gender: z
		.enum(['male', 'female', 'other'], {
			errorMap: () => ({ message: 'Please select a valid gender' }),
		})
		.optional(),

	icFinNumber: z
		.string()
		.min(8, 'IC/FIN number must be at least 8 characters')
		.max(12, 'IC/FIN number must not exceed 12 characters')
		.optional(),

	religion: z
		.enum(religionValues, {
			errorMap: () => ({ message: 'Please select a valid religion' }),
		})
		.optional(),

	race: z
		.enum(['chinese', 'eurasian', 'indian', 'malay', 'prefer-not-to-contribute'], {
			errorMap: () => ({ message: 'Please select a valid race' }),
		})
		.optional(),
});

// Family details schema
export const familyDetailsEditSchema = baseEditRequestSchema.extend({
	maritalStatus: z
		.enum(['single', 'married', 'divorced', 'widowed'], {
			errorMap: () => ({ message: 'Please select a valid marital status' }),
		})
		.optional(),

	spouseName: z
		.string()
		.min(2, 'Spouse name must be at least 2 characters')
		.max(50, 'Spouse name must not exceed 50 characters')
		.regex(/^[a-zA-Z\s]+$/, 'Spouse name must contain only letters and spaces')
		.optional(),

	spouseEmploymentStatus: z
		.enum(['employed', 'unemployed', 'self-employed', 'retired'], {
			errorMap: () => ({ message: 'Please select a valid employment status' }),
		})
		.optional(),
}).refine(
	(data) => {
		// If married, spouse name is required
		if (data.maritalStatus === 'married' && (!data.spouseName || data.spouseName.trim() === '')) {
			return false;
		}
		return true;
	},
	{
		message: 'Spouse name is required when marital status is married',
		path: ['spouseName'],
	}
);

// Contact details schema
export const contactDetailsEditSchema = baseEditRequestSchema.extend({
	name: z
		.string()
		.min(2, 'Contact name must be at least 2 characters')
		.max(50, 'Contact name must not exceed 50 characters')
		.regex(/^[a-zA-Z\s]+$/, 'Contact name must contain only letters and spaces')
		.optional(),

	relationship: z
		.enum(['Spouse', 'Parent', 'Child', 'Sibling', 'Friend', 'Other'], {
			errorMap: () => ({ message: 'Please select a valid relationship' }),
		})
		.optional(),

	countryDialCode: z
		.string()
		.regex(/^\+\d{1,4}$/, 'Invalid country dial code format')
		.optional(),

	phone: z
		.string()
		.min(8, 'Phone number must be at least 8 digits')
		.max(15, 'Phone number must not exceed 15 digits')
		.regex(/^\d+$/, 'Phone number must contain only digits')
		.optional(),

	email: z
		.string()
		.email('Invalid email format')
		.optional(),

	type: z
		.enum(['emergency', 'reference'], {
			errorMap: () => ({ message: 'Please select a valid contact type' }),
		})
		.optional(),
});

// Education details schema
export const educationDetailsEditSchema = baseEditRequestSchema.extend({
	instituteName: z
		.string()
		.min(2, 'Institution name must be at least 2 characters')
		.max(100, 'Institution name must not exceed 100 characters')
		.optional(),

	qualification: z
		.enum(['HIGH_SCHOOL', 'DIPLOMA', 'BACHELOR', 'POST_GRADUATE', 'DOCTORATE'], {
			errorMap: () => ({ message: 'Please select a valid qualification' }),
		})
		.optional(),

	startDate: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid start date format')
		.optional(),

	endDate: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid end date format')
		.optional(),

	grade: z
		.string()
		.min(1, 'Grade must be at least 1 character')
		.max(10, 'Grade must not exceed 10 characters')
		.optional(),
}).refine(
	(data) => {
		if (data.startDate && data.endDate) {
			return new Date(data.startDate) <= new Date(data.endDate);
		}
		return true;
	},
	{
		message: 'End date must be after start date',
		path: ['endDate'],
	}
);

// Experience details schema
export const experienceDetailsEditSchema = baseEditRequestSchema.extend({
	companyName: z
		.string()
		.min(2, 'Company name must be at least 2 characters')
		.max(100, 'Company name must not exceed 100 characters')
		.optional(),

	designation: z
		.string()
		.min(2, 'Job title must be at least 2 characters')
		.max(100, 'Job title must not exceed 100 characters')
		.optional(),

	periodFrom: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid start date format')
		.optional(),

	periodTo: z
		.string()
		.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid end date format')
		.optional(),

	location: z
		.string()
		.min(2, 'Location must be at least 2 characters')
		.max(100, 'Location must not exceed 100 characters')
		.optional(),

	reasonForLeaving: z
		.string()
		.max(500, 'Reason for leaving must not exceed 500 characters')
		.optional(),
}).refine(
	(data) => {
		if (data.periodFrom && data.periodTo) {
			return new Date(data.periodFrom) <= new Date(data.periodTo);
		}
		return true;
	},
	{
		message: 'End date must be after start date',
		path: ['periodTo'],
	}
);

// Function to get schema based on section
export const getEditRequestSchema = (section) => {
	switch (section) {
		case 'personal':
			return personalDetailsEditSchema;
		case 'family':
			return familyDetailsEditSchema;
		case 'contact':
			return contactDetailsEditSchema;
		case 'education':
			return educationDetailsEditSchema;
		case 'experience':
			return experienceDetailsEditSchema;
		default:
			return baseEditRequestSchema;
	}
};
