'use client';

import { DialogTrigger } from './ui/dialog';

import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from './ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Input } from './ui/input';
import { Label } from './ui/label';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from './ui/select';
import {
	User,
	Briefcase,
	GraduationCap,
	Users,
	Phone,
	Mail,
	MapPin,
	Calendar,
	Heart,
	Baby,
	Building,
	Award,
	Pencil,
	AlertCircle,
	CheckCircle2,
	Clock,
	Plus,
	ClipboardList,
	X,
	ChevronDown,
	ChevronUp,
} from 'lucide-react';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from './ui/dialog';
import { Textarea } from './ui/textarea';
import { useState } from 'react';
import { Alert, AlertDescription, AlertTitle } from './ui/alert';
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from './ui/collapsible';
import { useAppSelector } from '@/lib/hooks';

// Mock data from API response
const userData = {
	status: 200,
	message: 'Success',
	data: {
		_id: '67f95819eeb54af909e6c778',
		email: '<EMAIL>',
		role: 3,
    businessUnit: "Marin Branch",
    department: "Marin Branch",
    designation: "Marin Branch",
		personalDetails: {
			_id: '67f9648eb3f2b643dcb0db6e',
			employeeOrgId: 'C1XQ9L',
			nameOnNRIC: 'Walter White',
			countryDialCode: '+65',
			mobile: '65432187',
			gender: 'male',
			dateOfJoining: '2024-01-01T00:00:00.000Z',
			dob: '1964-01-01T00:00:00.000Z',
			age: 61,
			nationality: 'singapore',
			residentialStatus: 'Singapore Citizen',
			icFinNumber: '*********',
			issueDate: null,
			expiryDate: null,
			prStatus: 'singaporean',
			religion: 'christianity',
			race: 'indian',
			country: '67f959c8aeec84f398d9c52c',
			postalCode: '458600',
			streetName: 'BOON KENG ROAD',
			houseNo: '21',
			levelNo: '04',
			unitNo: '15',
			address: 'BLK 21,\r\n# 04-15,\r\nBOON KENG ROAD,\r\n458600',
			profilePhoto:
				'https://res.cloudinary.com/dv8nbs25p/image/upload/v1744397454/HarpHr/jlcd8vpd8mvz0vsdix9b.jpg',
			isDeleted: false,
			createdAt: '2025-04-11T18:50:54.894Z',
			updatedAt: '2025-04-11T18:50:54.971Z',
			__v: 0,
			userId: '67f95819eeb54af909e6c778',
		},
		reportingTo: {
			_id: '67f9648eb3f2b643dcb0db6e',
			employeeOrgId: 'C1XQ9L',
			nameOnNRIC: 'Walter White',
			countryDialCode: '+65',
			mobile: '65432187',
			gender: 'male',
			dateOfJoining: '2024-01-01T00:00:00.000Z',
			dob: '1964-01-01T00:00:00.000Z',
			age: 61,
			nationality: 'singapore',
			residentialStatus: 'Singapore Citizen',
			icFinNumber: '*********',
			issueDate: null,
			expiryDate: null,
			prStatus: 'singaporean',
			religion: 'christianity',
			race: 'indian',
			country: '67f959c8aeec84f398d9c52c',
			postalCode: '458600',
			streetName: 'BOON KENG ROAD',
			houseNo: '21',
			levelNo: '04',
			unitNo: '15',
			address: 'BLK 21,\r\n# 04-15,\r\nBOON KENG ROAD,\r\n458600',
			profilePhoto:
				'https://res.cloudinary.com/dv8nbs25p/image/upload/v1744397454/HarpHr/jlcd8vpd8mvz0vsdix9b.jpg',
			isDeleted: false,
			createdAt: '2025-04-11T18:50:54.894Z',
			updatedAt: '2025-04-11T18:50:54.971Z',
			__v: 0,
			userId: '67f95819eeb54af909e6c778',
		},
		education: [
			{
				_id: '67f96514b3f2b643dcb0db78',
				userId: '67f95819eeb54af909e6c778',
				instituteName: 'MIT',
				qualification: 'POST_GRADUATE',
				grade: 'A+',
				startDate: '1974-04-12T00:00:00.000Z',
				endDate: '1980-04-01T00:00:00.000Z',
				document: null,
				isDeleted: false,
				createdAt: '2025-04-11T18:53:08.413Z',
				updatedAt: '2025-04-11T18:53:08.413Z',
				__v: 0,
			},
			{
				_id: '67f96514b3f2b643dcb0db79',
				userId: '67f95819eeb54af909e6c778',
				instituteName: 'Stanford University',
				qualification: 'UNDER_GRADUATE',
				grade: 'A',
				startDate: '1970-09-01T00:00:00.000Z',
				endDate: '1974-06-30T00:00:00.000Z',
				document: null,
				isDeleted: false,
				createdAt: '2025-04-11T18:53:08.413Z',
				updatedAt: '2025-04-11T18:53:08.413Z',
				__v: 0,
			},
		],
		family: {
			_id: '67f9648fb3f2b643dcb0db72',
			userId: '67f95819eeb54af909e6c778',
			maritalStatus: 'married',
			spouseName: 'Pepper Potts',
			spouseEmploymentStatus: 'employed',
			children: [
				{
					age: 2,
					name: 'Morgan White',
					dob: '2023-03-02T00:00:00.000Z',
					nationality: 'singapore',
					_id: '67f9648fb3f2b643dcb0db73',
				},
			],
			isDeleted: false,
			createdAt: '2025-04-11T18:50:55.020Z',
			updatedAt: '2025-04-11T18:50:55.020Z',
			__v: 0,
		},
		experience: [
			{
				_id: '67f96514b3f2b643dcb0db7a',
				userId: '67f95819eeb54af909e6c778',
				location: 'India',
				companyName: 'Inventiko',
				designation: 'Senior Developer',
				periodFrom: '1982-04-01T00:00:00.000Z',
				periodTo: '2024-01-01T00:00:00.000Z',
				reasonForLeaving: '',
				document: null,
				isDeleted: false,
				createdAt: '2025-04-11T18:53:08.464Z',
				updatedAt: '2025-04-11T18:53:08.464Z',
				__v: 0,
			},
			{
				_id: '67f96514b3f2b643dcb0db7b',
				userId: '67f95819eeb54af909e6c778',
				location: 'Singapore',
				companyName: 'Tech Solutions Pte Ltd',
				designation: 'Software Engineer',
				periodFrom: '1980-07-01T00:00:00.000Z',
				periodTo: '1982-03-31T00:00:00.000Z',
				reasonForLeaving: 'Career advancement',
				document: null,
				isDeleted: false,
				createdAt: '2025-04-11T18:53:08.464Z',
				updatedAt: '2025-04-11T18:53:08.464Z',
				__v: 0,
			},
		],
		contact: [
			{
				_id: '67f96591b3f2b643dcb0db85',
				userId: '67f95819eeb54af909e6c778',
				type: 'emergency',
				name: 'Jane Doe',
				relationship: 'Spouse',
				phone: '65432178',
				countryDialCode: '+65',
				email: '<EMAIL>',
				isDeleted: false,
				createdAt: '2025-04-11T18:55:13.662Z',
				updatedAt: '2025-04-11T18:55:13.662Z',
				__v: 0,
			},
			{
				_id: '67f96591b3f2b643dcb0db86',
				userId: '67f95819eeb54af909e6c778',
				type: 'reference',
				name: 'John Smith',
				relationship: 'Friend',
				phone: '98765432',
				countryDialCode: '+65',
				email: '<EMAIL>',
				isDeleted: false,
				createdAt: '2025-04-11T18:55:13.662Z',
				updatedAt: '2025-04-11T18:55:13.662Z',
				__v: 0,
			},
		],
	},
	success: true,
};

// Helper function to format dates
const formatDate = (dateString) => {
	if (!dateString) return 'N/A';
	const date = new Date(dateString);
	return date.toLocaleDateString('en-US', {
		year: 'numeric',
		month: 'long',
		day: 'numeric',
	});
};

// Helper function to calculate duration
const calculateDuration = (startDate, endDate) => {
	const start = new Date(startDate);
	const end = new Date(endDate);
	const yearDiff = end.getFullYear() - start.getFullYear();
	const monthDiff = end.getMonth() - start.getMonth();

	let years = yearDiff;
	let months = monthDiff;

	if (monthDiff < 0) {
		years--;
		months += 12;
	}

	return `${years} years${months > 0 ? `, ${months} months` : ''}`;
};

// Helper function to capitalize words
const capitalize = (str) => {
	if (!str) return '';
	return str
		.split(' ')
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
		.join(' ');
};

// Helper function to format time ago
const timeAgo = (dateString) => {
	const date = new Date(dateString);
	const now = new Date();
	const seconds = Math.floor((now - date) / 1000);

	let interval = Math.floor(seconds / 31536000);
	if (interval >= 1) {
		return interval === 1 ? '1 year ago' : `${interval} years ago`;
	}

	interval = Math.floor(seconds / 2592000);
	if (interval >= 1) {
		return interval === 1 ? '1 month ago' : `${interval} months ago`;
	}

	interval = Math.floor(seconds / 86400);
	if (interval >= 1) {
		return interval === 1 ? '1 day ago' : `${interval} days ago`;
	}

	interval = Math.floor(seconds / 3600);
	if (interval >= 1) {
		return interval === 1 ? '1 hour ago' : `${interval} hours ago`;
	}

	interval = Math.floor(seconds / 60);
	if (interval >= 1) {
		return interval === 1 ? '1 minute ago' : `${interval} minutes ago`;
	}

	return 'Just now';
};

export function ProfilePageComponent() {
	// const { data } = userData;
	// const { personalDetails, education, family, experience, contact } = data;
	const [notifications, setNotifications] = useState([]);
	const [pendingChanges, setPendingChanges] = useState([
		{
			id: 1,
			field: 'Institute Name',
			section: 'Education',
			oldValue: 'MIT',
			newValue: 'Harvard University',
			status: 'pending',
			requestedAt: '2025-04-20T10:30:00.000Z',
			reason: 'Correction of my educational history',
		},
		{
			id: 2,
			field: 'Spouse Name',
			section: 'Family',
			oldValue: 'Pepper Potts',
			newValue: 'Skyler White',
			status: 'pending',
			requestedAt: '2025-04-22T14:15:00.000Z',
			reason: 'Updated after marriage',
		},
		{
			id: 3,
			field: 'Add Child',
			section: 'Family',
			oldValue: '-',
			newValue: 'Walter White Jr.',
			status: 'pending',
			requestedAt: '2025-04-23T09:45:00.000Z',
			reason: 'Adding new family member',
		},
	]);
	const [showPendingRequests, setShowPendingRequests] = useState(false);
	const [cancelRequestId, setCancelRequestId] = useState(null);
  const { employeeDetails } = useAppSelector((store) => store.employee);
  console.log(employeeDetails)

	const addNotification = (message, type = 'success') => {
		const id = Date.now();
		setNotifications((prev) => [...prev, { id, message, type }]);
		setTimeout(() => {
			setNotifications((prev) =>
				prev.filter((notification) => notification.id !== id)
			);
		}, 5000);
	};

	const handleEditRequest = (section, field, oldValue, newValue, reason) => {
		// In a real app, you would send the edit request to the server here
		const id = Date.now();
		setPendingChanges((prev) => [
			...prev,
			{
				id,
				field,
				section,
				oldValue,
				newValue,
				status: 'pending',
				requestedAt: new Date().toISOString(),
				reason,
			},
		]);
		addNotification(
			`Your request to update ${field} has been submitted for review.`
		);
	};

	const handleAddChild = (childData) => {
		const newChild = {
			name: childData.name,
			age: childData.age,
			dob: childData.dob,
			nationality: childData.nationality,
		};

		handleEditRequest(
			'Family',
			'Add Child',
			'-',
			childData.name,
			childData.reason
		);

		addNotification(
			`Your request to add ${childData.name} has been submitted for review.`
		);
	};

	const handleAddContact = (contactData) => {
		const newContact = {
			type: contactData.type,
			name: contactData.name,
			relationship: contactData.relationship,
			phone: contactData.phone,
			countryDialCode: contactData.countryDialCode,
			email: contactData.email,
		};

		handleEditRequest(
			'Contact',
			'Add Contact',
			'-',
			contactData.name,
			contactData.reason
		);

		addNotification(
			`Your request to add ${contactData.name} as a contact has been submitted for review.`
		);
	};

	const handleCancelRequest = (id) => {
		setPendingChanges((prev) => prev.filter((change) => change.id !== id));
		setCancelRequestId(null);
		addNotification('Your request has been cancelled.', 'success');
	};

	return (
		<div className="container mx-auto p-4">
			{/* Hero Section with Profile Overview */}
			<div className="relative mb-8 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl overflow-hidden">
				<div className="absolute inset-0 bg-black opacity-10 z-0"></div>
				<div className="relative z-10 p-8 flex flex-col md:flex-row items-center gap-6 text-white">
					<div className="relative">
						<Avatar className="h-32 w-32 border-4 border-white shadow-lg">
							<AvatarImage
								src={personalDetails.profilePhoto || '/placeholder.svg'}
								alt={personalDetails.nameOnNRIC}
							/>
							<AvatarFallback className="text-3xl bg-indigo-700">
								{personalDetails.nameOnNRIC
									.split(' ')
									.map((n) => n[0])
									.join('')}
							</AvatarFallback>
						</Avatar>
						<Badge className="absolute -bottom-2 right-0 bg-green-500 border-2 border-white">
							Active
						</Badge>
					</div>

					<div className="text-center md:text-left">
						<h1 className="text-3xl font-bold">{personalDetails.nameOnNRIC}</h1>
						<div className="flex flex-wrap justify-center md:justify-start gap-2 mt-2">
							<Badge
								variant="outline"
								className="bg-white/20 text-white border-white/40"
							>
								ID: {personalDetails.employeeOrgId}
							</Badge>
							<Badge
								variant="outline"
								className="bg-white/20 text-white border-white/40"
							>
								{experience && experience.length > 0 ? experience[0].designation : 'N/A'}
							</Badge>
							<Badge
								variant="outline"
								className="bg-white/20 text-white border-white/40"
							>
								{capitalize(personalDetails.nationality)}
							</Badge>
						</div>
						<div className="mt-4 flex flex-wrap justify-center md:justify-start gap-4">
							<div className="flex items-center gap-1">
								<Mail className="h-4 w-4" />
								<span>{data.email}</span>
							</div>
							<div className="flex items-center gap-1">
								<Phone className="h-4 w-4" />
								<span>
									{personalDetails.countryDialCode} {personalDetails.mobile}
								</span>
							</div>
							<div className="flex items-center gap-1">
								<Calendar className="h-4 w-4" />
								<span>Joined: {formatDate(personalDetails.dateOfJoining)}</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Notifications */}
			<div className="fixed top-4 right-4 z-50 space-y-2 max-w-md">
				{notifications.map((notification) => (
					<Alert
						key={notification.id}
						className={`border-${
							notification.type === 'success' ? 'green' : 'amber'
						}-500 bg-${notification.type === 'success' ? 'green' : 'amber'}-50 shadow-md transition-all duration-300 ease-in-out`}
					>
						{notification.type === 'success' ? (
							<CheckCircle2 className="h-5 w-5 text-green-600" />
						) : (
							<AlertCircle className="h-5 w-5 text-amber-600" />
						)}
						<AlertDescription
							className={`text-${notification.type === 'success' ? 'green' : 'amber'}-700`}
						>
							{notification.message}
						</AlertDescription>
					</Alert>
				))}
			</div>

			{/* Pending Changes Section */}
			{pendingChanges.length > 0 && (
				<Collapsible
					open={showPendingRequests}
					onOpenChange={setShowPendingRequests}
					className="mb-6 border rounded-lg overflow-hidden"
				>
					<div className="bg-amber-50 border-b border-amber-200">
						<CollapsibleTrigger className="flex items-center justify-between w-full p-4 text-left">
							<div className="flex items-center gap-2">
								<Clock className="h-5 w-5 text-amber-600" />
								<div>
									<h3 className="font-medium text-amber-800">
										Pending Change Requests
									</h3>
									<p className="text-sm text-amber-700">
										You have {pendingChanges.length} pending change request
										{pendingChanges.length > 1 ? 's' : ''}
									</p>
								</div>
							</div>
							<div className="flex items-center gap-2">
								<Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">
									{pendingChanges.length} Pending
								</Badge>
								{showPendingRequests ? (
									<ChevronUp className="h-5 w-5 text-amber-600" />
								) : (
									<ChevronDown className="h-5 w-5 text-amber-600" />
								)}
							</div>
						</CollapsibleTrigger>
					</div>
					<CollapsibleContent>
						<div className="p-4 bg-white">
							<div className="space-y-4">
								{pendingChanges.map((change) => (
									<div
										key={change.id}
										className="border rounded-md p-4 bg-slate-50"
									>
										<div className="flex justify-between items-start">
											<div>
												<div className="flex items-center gap-2">
													<Badge
														className={
															change.status === 'pending'
																? 'bg-amber-100 text-amber-800'
																: 'bg-green-100 text-green-800'
														}
													>
														{change.status === 'pending'
															? 'Pending Approval'
															: 'Approved'}
													</Badge>
													<span className="text-sm text-muted-foreground">
														{timeAgo(change.requestedAt)}
													</span>
												</div>
												<h4 className="font-medium mt-2">
													{change.section} - {change.field}
												</h4>
												<div className="mt-1 grid grid-cols-2 gap-4">
													<div>
														<p className="text-xs text-muted-foreground">
															Current Value
														</p>
														<p className="text-sm">{change.oldValue}</p>
													</div>
													<div>
														<p className="text-xs text-muted-foreground">
															Requested Value
														</p>
														<p className="text-sm font-medium">
															{change.newValue}
														</p>
													</div>
												</div>
												{change.reason && (
													<div className="mt-2">
														<p className="text-xs text-muted-foreground">
															Reason
														</p>
														<p className="text-sm">{change.reason}</p>
													</div>
												)}
											</div>
											<Button
												variant="ghost"
												size="icon"
												className="h-8 w-8"
												onClick={() => setCancelRequestId(change.id)}
											>
												<X className="h-4 w-4" />
											</Button>
										</div>
									</div>
								))}
							</div>
						</div>
					</CollapsibleContent>
				</Collapsible>
			)}

			{/* Main Content with Tabs */}
			<Tabs defaultValue="personal" className="w-full">
				<TabsList className="grid grid-cols-5 mb-8">
					<TabsTrigger value="personal" className="flex items-center gap-2">
						<User className="h-4 w-4" />
						<span className="hidden sm:inline">Personal</span>
					</TabsTrigger>
					<TabsTrigger value="family" className="flex items-center gap-2">
						<Users className="h-4 w-4" />
						<span className="hidden sm:inline">Family</span>
					</TabsTrigger>
					<TabsTrigger value="education" className="flex items-center gap-2">
						<GraduationCap className="h-4 w-4" />
						<span className="hidden sm:inline">Education</span>
					</TabsTrigger>
					<TabsTrigger value="experience" className="flex items-center gap-2">
						<Briefcase className="h-4 w-4" />
						<span className="hidden sm:inline">Experience</span>
					</TabsTrigger>
					<TabsTrigger value="contact" className="flex items-center gap-2">
						<Phone className="h-4 w-4" />
						<span className="hidden sm:inline">Contact</span>
					</TabsTrigger>
				</TabsList>

				{/* Personal Details Tab */}
				<TabsContent value="personal">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<Card className="overflow-hidden">
							<CardHeader className="bg-slate-50">
								<CardTitle className="flex items-center gap-2">
									<User className="h-5 w-5 text-indigo-600" />
									Personal Information
								</CardTitle>
								<CardDescription>
									Personal details cannot be edited. Please contact HR for any
									corrections.
								</CardDescription>
							</CardHeader>
							<CardContent className="pt-6">
								<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
									<div>
										<p className="text-sm text-muted-foreground">Full Name</p>
										<p className="font-medium">{personalDetails.nameOnNRIC}</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">Employee ID</p>
										<p className="font-medium">
											{personalDetails.employeeOrgId}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">
											Date of Birth
										</p>
										<p className="font-medium">
											{formatDate(personalDetails.dob)}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">Age</p>
										<p className="font-medium">{personalDetails.age} years</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">Gender</p>
										<p className="font-medium">
											{capitalize(personalDetails.gender)}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">
											IC/FIN Number
										</p>
										<p className="font-medium">{personalDetails.icFinNumber}</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">Religion</p>
										<p className="font-medium">
											{capitalize(personalDetails.religion)}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">Race</p>
										<p className="font-medium">
											{capitalize(personalDetails.race)}
										</p>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card className="overflow-hidden">
							<CardHeader className="bg-slate-50">
								<CardTitle className="flex items-center gap-2">
									<MapPin className="h-5 w-5 text-indigo-600" />
									Address & Citizenship
								</CardTitle>
								<CardDescription>
									Address and citizenship details cannot be edited. Please
									contact HR for any corrections.
								</CardDescription>
							</CardHeader>
							<CardContent className="pt-6">
								<div className="mb-6">
									<p className="text-sm text-muted-foreground">Address</p>
									<p className="font-medium whitespace-pre-line">
										{personalDetails.address}
									</p>
								</div>

								<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
									<div>
										<p className="text-sm text-muted-foreground">Postal Code</p>
										<p className="font-medium">{personalDetails.postalCode}</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">Street Name</p>
										<p className="font-medium">{personalDetails.streetName}</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">Nationality</p>
										<p className="font-medium">
											{capitalize(personalDetails.nationality)}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">
											Residential Status
										</p>
										<p className="font-medium">
											{personalDetails.residentialStatus}
										</p>
									</div>
								</div>
							</CardContent>
						</Card>
					</div>
				</TabsContent>

				{/* Education Tab */}
				<TabsContent value="education">
					<Card>
						<CardHeader className="bg-slate-50">
							<CardTitle className="flex items-center gap-2">
								<GraduationCap className="h-5 w-5 text-indigo-600" />
								Educational Background
							</CardTitle>
							<CardDescription>
								Click the edit icon next to any field to request changes.
								Updates will be reflected after approval.
							</CardDescription>
						</CardHeader>
						<CardContent className="pt-6">
							{education && education.length > 0 ? (
								<div className="space-y-6">
									{education.map((edu, index) => (
										<div key={edu._id} className="relative pl-8 border-l-2 border-indigo-200 pb-6">
											<div className="absolute -left-3 top-0 bg-indigo-600 rounded-full p-1">
												<GraduationCap className="h-4 w-4 text-white" />
											</div>
											<div className="mb-2">
												<Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">
													{formatDate(edu.startDate)} -{' '}
													{formatDate(edu.endDate)}
												</Badge>
												<Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-200">
													{calculateDuration(edu.startDate, edu.endDate)}
												</Badge>
												<EditFieldButton
													section="Education"
													field={`Education ${index + 1} Duration`}
													currentValue={`${formatDate(edu.startDate)} - ${formatDate(edu.endDate)}`}
													onSubmit={handleEditRequest}
													inputType="dateRange"
												/>
											</div>
											<div className="flex items-center">
												<h3 className="text-xl font-bold">
													{edu.instituteName}
												</h3>
												<EditFieldButton
													section="Education"
													field={`Education ${index + 1} Institute Name`}
													currentValue={edu.instituteName}
													onSubmit={handleEditRequest}
												/>
											</div>
											<div className="flex items-center">
												<p className="text-muted-foreground">
													{edu.qualification.replace('_', ' ')}
												</p>
												<EditFieldButton
													section="Education"
													field={`Education ${index + 1} Qualification`}
													currentValue={edu.qualification.replace('_', ' ')}
													onSubmit={handleEditRequest}
													inputType="select"
													options={[
														{ value: 'HIGH_SCHOOL', label: 'High School' },
														{ value: 'DIPLOMA', label: 'Diploma' },
														{ value: 'BACHELOR', label: "Bachelor's Degree" },
														{ value: 'POST_GRADUATE', label: 'Post Graduate' },
														{ value: 'DOCTORATE', label: 'Doctorate' },
													]}
												/>
											</div>
											<div className="mt-2 flex items-center">
												<Award className="h-4 w-4 text-amber-500 mr-1" />
												<span className="font-medium">Grade: {edu.grade}</span>
												<EditFieldButton
													section="Education"
													field={`Education ${index + 1} Grade`}
													currentValue={edu.grade}
													onSubmit={handleEditRequest}
												/>
											</div>
										</div>
									))}
								</div>
							) : (
								<p className="text-muted-foreground">
									No education information available.
								</p>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				{/* Experience Tab */}
				<TabsContent value="experience">
					<Card>
						<CardHeader className="bg-slate-50">
							<CardTitle className="flex items-center gap-2">
								<Briefcase className="h-5 w-5 text-indigo-600" />
								Professional Experience
							</CardTitle>
							<CardDescription>
								Click the edit icon next to any field to request changes.
								Updates will be reflected after approval.
							</CardDescription>
						</CardHeader>
						<CardContent className="pt-6">
							{experience && experience.length > 0 ? (
								<div className="space-y-6">
									{experience.map((exp, index) => (
										<div key={exp._id} className="relative pl-8 border-l-2 border-indigo-200 pb-6">
											<div className="absolute -left-3 top-0 bg-indigo-600 rounded-full p-1">
												<Building className="h-4 w-4 text-white" />
											</div>
											<div className="mb-2">
												<Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">
													{formatDate(exp.periodFrom)} -{' '}
													{formatDate(exp.periodTo)}
												</Badge>
												<Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-200">
													{calculateDuration(
														exp.periodFrom,
														exp.periodTo
													)}
												</Badge>
												<EditFieldButton
													section="Experience"
													field={`Experience ${index + 1} Duration`}
													currentValue={`${formatDate(exp.periodFrom)} - ${formatDate(exp.periodTo)}`}
													onSubmit={handleEditRequest}
													inputType="dateRange"
												/>
											</div>
											<div className="flex items-center">
												<h3 className="text-xl font-bold">
													{exp.companyName}
												</h3>
												<EditFieldButton
													section="Experience"
													field={`Experience ${index + 1} Company Name`}
													currentValue={exp.companyName}
													onSubmit={handleEditRequest}
												/>
											</div>
											<div className="flex items-center">
												<p className="text-lg font-medium">
													{exp.designation}
												</p>
												<EditFieldButton
													section="Experience"
													field={`Experience ${index + 1} Designation`}
													currentValue={exp.designation}
													onSubmit={handleEditRequest}
												/>
											</div>
											<div className="mt-2 flex items-center">
												<MapPin className="h-4 w-4 text-rose-500 mr-1" />
												<span>{exp.location}</span>
												<EditFieldButton
													section="Experience"
													field={`Experience ${index + 1} Location`}
													currentValue={exp.location}
													onSubmit={handleEditRequest}
												/>
											</div>
											{exp.reasonForLeaving && (
												<div className="mt-2 flex items-center">
													<span className="text-sm text-muted-foreground">
														Reason for leaving: {exp.reasonForLeaving}
													</span>
													<EditFieldButton
														section="Experience"
														field={`Experience ${index + 1} Reason for Leaving`}
														currentValue={exp.reasonForLeaving}
														onSubmit={handleEditRequest}
													/>
												</div>
											)}
										</div>
									))}
								</div>
							) : (
								<p className="text-muted-foreground">
									No experience information available.
								</p>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				{/* Family Tab */}
				<TabsContent value="family">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<Card className="overflow-hidden">
							<CardHeader className="bg-slate-50">
								<CardTitle className="flex items-center gap-2">
									<Heart className="h-5 w-5 text-rose-500" />
									Marital Status
								</CardTitle>
								<CardDescription>
									Click the edit icon next to any field to request changes.
									Updates will be reflected after approval.
								</CardDescription>
							</CardHeader>
							<CardContent className="pt-6">
								<div className="grid gap-4">
									<div className="flex items-center justify-between">
										<div>
											<p className="text-sm text-muted-foreground">
												Marital Status
											</p>
											<p className="font-medium">
												{capitalize(family.maritalStatus)}
											</p>
										</div>
										<EditFieldButton
											section="Family"
											field="Marital Status"
											currentValue={family.maritalStatus}
											onSubmit={handleEditRequest}
											inputType="select"
											options={[
												{ value: 'single', label: 'Single' },
												{ value: 'married', label: 'Married' },
												{ value: 'divorced', label: 'Divorced' },
												{ value: 'widowed', label: 'Widowed' },
											]}
										/>
									</div>
									<div className="flex items-center justify-between">
										<div>
											<p className="text-sm text-muted-foreground">
												Spouse Name
											</p>
											<p className="font-medium">{family.spouseName}</p>
										</div>
										<EditFieldButton
											section="Family"
											field="Spouse Name"
											currentValue={family.spouseName}
											onSubmit={handleEditRequest}
										/>
									</div>
									<div className="flex items-center justify-between">
										<div>
											<p className="text-sm text-muted-foreground">
												Spouse Employment Status
											</p>
											<p className="font-medium">
												{capitalize(family.spouseEmploymentStatus)}
											</p>
										</div>
										<EditFieldButton
											section="Family"
											field="Spouse Employment Status"
											currentValue={family.spouseEmploymentStatus}
											onSubmit={handleEditRequest}
											inputType="select"
											options={[
												{ value: 'employed', label: 'Employed' },
												{ value: 'unemployed', label: 'Unemployed' },
												{ value: 'self-employed', label: 'Self-Employed' },
												{ value: 'retired', label: 'Retired' },
											]}
										/>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card className="overflow-hidden">
							<CardHeader className="bg-slate-50">
								<div className="flex justify-between items-center">
									<CardTitle className="flex items-center gap-2">
										<Baby className="h-5 w-5 text-blue-500" />
										Children
									</CardTitle>
									<AddChildDialog onSubmit={handleAddChild} />
								</div>
								<CardDescription>
									Click the edit icon next to any field to request changes.
									Updates will be reflected after approval.
								</CardDescription>
							</CardHeader>
							<CardContent className="pt-6">
								{family.children && family.children.length > 0 ? (
									<div className="space-y-4">
										{family.children.map((child, index) => (
											<div
												key={index}
												className="p-4 rounded-lg border bg-slate-50"
											>
												<div className="flex justify-between items-start">
													<div>
														<div className="flex items-center">
															<h4 className="font-semibold">{child.name}</h4>
															<EditFieldButton
																section="Family"
																field={`Child ${index + 1} Name`}
																currentValue={child.name}
																onSubmit={handleEditRequest}
															/>
														</div>
														<div className="flex items-center">
															<p className="text-sm text-muted-foreground">
																Age: {child.age} years
															</p>
															<EditFieldButton
																section="Family"
																field={`Child ${index + 1} Age`}
																currentValue={child.age.toString()}
																onSubmit={handleEditRequest}
																inputType="number"
															/>
														</div>
														<div className="flex items-center mt-1">
															<p className="text-sm text-muted-foreground">
																Nationality: {capitalize(child.nationality)}
															</p>
															<EditFieldButton
																section="Family"
																field={`Child ${index + 1} Nationality`}
																currentValue={child.nationality}
																onSubmit={handleEditRequest}
															/>
														</div>
													</div>
													<div className="flex items-center">
														<Badge className="bg-blue-100 text-blue-800">
															DOB: {formatDate(child.dob)}
														</Badge>
														<EditFieldButton
															section="Family"
															field={`Child ${index + 1} DOB`}
															currentValue={child.dob}
															onSubmit={handleEditRequest}
															inputType="date"
														/>
													</div>
												</div>
											</div>
										))}
									</div>
								) : (
									<p className="text-muted-foreground">
										No children information available.
									</p>
								)}
							</CardContent>
						</Card>
					</div>
				</TabsContent>

				{/* Contact Tab */}
				<TabsContent value="contact">
					<Card>
						<CardHeader className="bg-slate-50">
							<div className="flex justify-between items-center">
								<div>
									<CardTitle className="flex items-center gap-2">
										<Phone className="h-5 w-5 text-indigo-600" />
										Contact Information
									</CardTitle>
									<CardDescription>
										Click the edit icon next to any field to request changes.
										Updates will be reflected after approval.
									</CardDescription>
								</div>
								<AddContactDialog onSubmit={handleAddContact} />
							</div>
						</CardHeader>
						<CardContent className="pt-6">
							{contact && contact.length > 0 ? (
								<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
									{contact.map((cont, index) => (
										<div key={cont._id} className="p-4 rounded-lg border bg-slate-50">
											<div className="flex items-center justify-between mb-4">
												<h3 className="font-semibold text-lg">{cont.name}</h3>
												<EditFieldButton
													section="Contact"
													field={`Contact ${index + 1} Name`}
													currentValue={cont.name}
													onSubmit={handleEditRequest}
												/>
											</div>
											<div className="space-y-3">
												<div className="flex items-center justify-between">
													<Badge className="bg-indigo-100 text-indigo-800">
														{cont.relationship}
													</Badge>
													<EditFieldButton
														section="Contact"
														field={`Contact ${index + 1} Relationship`}
														currentValue={cont.relationship}
														onSubmit={handleEditRequest}
														inputType="select"
														options={[
															{ value: 'Spouse', label: 'Spouse' },
															{ value: 'Parent', label: 'Parent' },
															{ value: 'Child', label: 'Child' },
															{ value: 'Sibling', label: 'Sibling' },
															{ value: 'Friend', label: 'Friend' },
															{ value: 'Other', label: 'Other' },
														]}
													/>
												</div>
												<div className="flex items-center justify-between">
													<div className="flex items-center gap-2">
														<Phone className="h-4 w-4 text-muted-foreground" />
														<span>
															{cont.countryDialCode} {cont.phone}
														</span>
													</div>
													<EditFieldButton
														section="Contact"
														field={`Contact ${index + 1} Phone Number`}
														currentValue={`${cont.countryDialCode} ${cont.phone}`}
														onSubmit={handleEditRequest}
														inputType="phone"
													/>
												</div>
												<div className="flex items-center justify-between">
													<div className="flex items-center gap-2">
														<Mail className="h-4 w-4 text-muted-foreground" />
														<span>{cont.email}</span>
													</div>
													<EditFieldButton
														section="Contact"
														field={`Contact ${index + 1} Email`}
														currentValue={cont.email}
														onSubmit={handleEditRequest}
														inputType="email"
													/>
												</div>
												<div className="flex items-center justify-between">
													<div className="flex items-center gap-2">
														<ClipboardList className="h-4 w-4 text-muted-foreground" />
														<span>{capitalize(cont.type)}</span>
													</div>
													<EditFieldButton
														section="Contact"
														field={`Contact ${index + 1} Type`}
														currentValue={cont.type}
														onSubmit={handleEditRequest}
														inputType="select"
														options={[
															{ value: 'emergency', label: 'Emergency' },
															{ value: 'reference', label: 'Reference' },
														]}
													/>
												</div>
											</div>
										</div>
									))}
								</div>
							) : (
								<p className="text-muted-foreground">
									No contact information available.
								</p>
							)}
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
			{/* Confirmation Dialog for Cancelling Request */}
			<Dialog
				open={cancelRequestId !== null}
				onOpenChange={(open) => !open && setCancelRequestId(null)}
			>
				<DialogContent className="max-w-md">
					<DialogHeader>
						<DialogTitle>Cancel Request</DialogTitle>
						<DialogDescription>
							Are you sure you want to cancel this request? This action cannot
							be undone.
						</DialogDescription>
					</DialogHeader>
					<div className="py-4">
						{cancelRequestId && (
							<div className="p-3 bg-slate-50 rounded-md border">
								<p className="font-medium">
									{
										pendingChanges.find((c) => c.id === cancelRequestId)
											?.section
									}{' '}
									-{' '}
									{pendingChanges.find((c) => c.id === cancelRequestId)?.field}
								</p>
								<div className="mt-1 text-sm text-muted-foreground">
									<p>
										Requested change:{' '}
										{
											pendingChanges.find((c) => c.id === cancelRequestId)
												?.newValue
										}
									</p>
									<p className="mt-1">
										Submitted{' '}
										{timeAgo(
											pendingChanges.find((c) => c.id === cancelRequestId)
												?.requestedAt
										)}
									</p>
								</div>
							</div>
						)}
					</div>
					<DialogFooter>
						<Button variant="outline" onClick={() => setCancelRequestId(null)}>
							Keep Request
						</Button>
						<Button
							variant="destructive"
							onClick={() => handleCancelRequest(cancelRequestId)}
						>
							Cancel Request
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}

function EditFieldButton({
	section,
	field,
	currentValue,
	onSubmit,
	inputType = 'text',
	options = [],
}) {
	const [open, setOpen] = useState(false);
	const [newValue, setNewValue] = useState(currentValue);
	const [startDate, setStartDate] = useState('');
	const [endDate, setEndDate] = useState('');
	const [reason, setReason] = useState('');

	const handleSubmit = (e) => {
		e.preventDefault();
		let finalValue = newValue;

		if (inputType === 'dateRange') {
			finalValue = `${startDate} - ${endDate}`;
		}

		onSubmit(section, field, currentValue, finalValue, reason);
		setOpen(false);
	};

	const renderInput = () => {
		switch (inputType) {
			case 'select':
				return (
					<Select
						defaultValue={currentValue.toLowerCase()}
						onValueChange={(value) => setNewValue(value)}
					>
						<SelectTrigger className="w-full">
							<SelectValue placeholder="Select..." />
						</SelectTrigger>
						<SelectContent>
							{options.map((option) => (
								<SelectItem key={option.value} value={option.value}>
									{option.label}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				);
			case 'textarea':
				return (
					<Textarea
						value={newValue}
						onChange={(e) => setNewValue(e.target.value)}
						placeholder={`Enter new ${field.toLowerCase()}`}
						className="min-h-[100px]"
					/>
				);
			case 'number':
				return (
					<Input
						type="number"
						value={newValue}
						onChange={(e) => setNewValue(e.target.value)}
						placeholder={`Enter new ${field.toLowerCase()}`}
					/>
				);
			case 'email':
				return (
					<Input
						type="email"
						value={newValue}
						onChange={(e) => setNewValue(e.target.value)}
						placeholder={`Enter new ${field.toLowerCase()}`}
					/>
				);
			case 'phone':
				return (
					<Input
						type="tel"
						value={newValue}
						onChange={(e) => setNewValue(e.target.value)}
						placeholder={`Enter new ${field.toLowerCase()}`}
					/>
				);
			case 'date':
				return (
					<Input
						type="date"
						value={newValue}
						onChange={(e) => setNewValue(e.target.value)}
						placeholder={`Enter new ${field.toLowerCase()}`}
					/>
				);
			case 'dateRange':
				return (
					<div className="grid gap-4">
						<div>
							<Label htmlFor="start-date">Start Date</Label>
							<Input
								id="start-date"
								type="date"
								value={startDate}
								onChange={(e) => setStartDate(e.target.value)}
							/>
						</div>
						<div>
							<Label htmlFor="end-date">End Date</Label>
							<Input
								id="end-date"
								type="date"
								value={endDate}
								onChange={(e) => setEndDate(e.target.value)}
							/>
						</div>
					</div>
				);
			default:
				return (
					<Input
						type="text"
						value={newValue}
						onChange={(e) => setNewValue(e.target.value)}
						placeholder={`Enter new ${field.toLowerCase()}`}
					/>
				);
		}
	};

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>
				<Button variant="ghost" size="icon" className="h-6 w-6 ml-2">
					<Pencil className="h-3 w-3" />
				</Button>
			</DialogTrigger>
			<DialogContent className="max-h-[85vh] overflow-hidden flex flex-col">
				<form onSubmit={handleSubmit} className="flex flex-col h-full">
					<DialogHeader>
						<DialogTitle>Edit {field}</DialogTitle>
						<DialogDescription>
							Request a change to your {field.toLowerCase()}. This change will
							need approval before it appears on your profile.
						</DialogDescription>
					</DialogHeader>
					<div className="py-4 overflow-y-auto flex-1">
						<Alert className="mb-4">
							<AlertCircle className="h-4 w-4" />
							<AlertTitle>Approval Required</AlertTitle>
							<AlertDescription>
								Changes will be reviewed by your reporting manager before being
								updated in your profile.
							</AlertDescription>
						</Alert>
						<div className="space-y-4">
							<div>
								<Label
									htmlFor="current-value"
									className="block text-sm font-medium mb-1"
								>
									Current Value
								</Label>
								<Input
									id="current-value"
									value={currentValue}
									disabled
									className="bg-slate-50"
								/>
							</div>
							<div>
								<Label
									htmlFor="new-value"
									className="block text-sm font-medium mb-1"
								>
									New Value
								</Label>
								{renderInput()}
							</div>
							<div>
								<Label
									htmlFor="reason"
									className="block text-sm font-medium mb-1"
								>
									Reason for Change
								</Label>
								<Textarea
									id="reason"
									placeholder="Please provide a reason for this change..."
									className="min-h-[80px]"
									value={reason}
									onChange={(e) => setReason(e.target.value)}
									required
								/>
							</div>
						</div>
					</div>
					<DialogFooter>
						<Button
							type="button"
							variant="outline"
							onClick={() => setOpen(false)}
						>
							Cancel
						</Button>
						<Button type="submit">Submit Request</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}

function AddChildDialog({ onSubmit }) {
	const [open, setOpen] = useState(false);
	const [childData, setChildData] = useState({
		name: '',
		age: '',
		dob: '',
		nationality: '',
		reason: '',
	});

	const handleChange = (e) => {
		const { name, value } = e.target;
		setChildData((prev) => ({ ...prev, [name]: value }));
	};

	const handleSubmit = (e) => {
		e.preventDefault();
		onSubmit(childData);
		setOpen(false);
		setChildData({
			name: '',
			age: '',
			dob: '',
			nationality: '',
			reason: '',
		});
	};

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>
				<Button variant="outline" size="sm" className="flex items-center gap-1">
					<Plus className="h-4 w-4" />
					Add Child
				</Button>
			</DialogTrigger>
			<DialogContent className="max-h-[85vh] overflow-hidden flex flex-col">
				<form onSubmit={handleSubmit} className="flex flex-col h-full">
					<DialogHeader>
						<DialogTitle>Add Child</DialogTitle>
						<DialogDescription>
							Request to add a child to your profile. This addition will need
							approval before it appears on your profile.
						</DialogDescription>
					</DialogHeader>
					<div className="py-4 overflow-y-auto flex-1">
						<Alert className="mb-4">
							<AlertCircle className="h-4 w-4" />
							<AlertTitle>Approval Required</AlertTitle>
							<AlertDescription>
								This addition will be reviewed by your reporting manager before
								being updated in your profile.
							</AlertDescription>
						</Alert>
						<div className="space-y-4">
							<div>
								<Label
									htmlFor="name"
									className="block text-sm font-medium mb-1"
								>
									Child&apos;s Name
								</Label>
								<Input
									id="name"
									name="name"
									value={childData.name}
									onChange={handleChange}
									placeholder="Enter child's name"
									required
								/>
							</div>
							<div>
								<Label htmlFor="age" className="block text-sm font-medium mb-1">
									Age
								</Label>
								<Input
									id="age"
									name="age"
									type="number"
									value={childData.age}
									onChange={handleChange}
									placeholder="Enter child's age"
									required
								/>
							</div>
							<div>
								<Label htmlFor="dob" className="block text-sm font-medium mb-1">
									Date of Birth
								</Label>
								<Input
									id="dob"
									name="dob"
									type="date"
									value={childData.dob}
									onChange={handleChange}
									required
								/>
							</div>
							<div>
								<Label
									htmlFor="nationality"
									className="block text-sm font-medium mb-1"
								>
									Nationality
								</Label>
								<Input
									id="nationality"
									name="nationality"
									value={childData.nationality}
									onChange={handleChange}
									placeholder="Enter child's nationality"
									required
								/>
							</div>
							<div>
								<Label
									htmlFor="reason"
									className="block text-sm font-medium mb-1"
								>
									Reason for Addition
								</Label>
								<Textarea
									id="reason"
									name="reason"
									placeholder="Please provide a reason for this addition..."
									className="min-h-[80px]"
									value={childData.reason}
									onChange={handleChange}
									required
								/>
							</div>
						</div>
					</div>
					<DialogFooter>
						<Button
							type="button"
							variant="outline"
							onClick={() => setOpen(false)}
						>
							Cancel
						</Button>
						<Button type="submit">Submit Request</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}

function AddContactDialog({ onSubmit }) {
	const [open, setOpen] = useState(false);
	const [contactData, setContactData] = useState({
		type: 'emergency',
		name: '',
		relationship: '',
		countryDialCode: '+65',
		phone: '',
		email: '',
		reason: '',
	});

	const handleChange = (e) => {
		const { name, value } = e.target;
		setContactData((prev) => ({ ...prev, [name]: value }));
	};

	const handleSelectChange = (name, value) => {
		setContactData((prev) => ({ ...prev, [name]: value }));
	};

	const handleSubmit = (e) => {
		e.preventDefault();
		onSubmit(contactData);
		setOpen(false);
		setContactData({
			type: 'emergency',
			name: '',
			relationship: '',
			countryDialCode: '+65',
			phone: '',
			email: '',
			reason: '',
		});
	};

	return (
		<Dialog open={open} onOpenChange={setOpen}>
			<DialogTrigger asChild>
				<Button variant="outline" size="sm" className="flex items-center gap-1">
					<Plus className="h-4 w-4" />
					Add Contact
				</Button>
			</DialogTrigger>
			<DialogContent className="max-h-[85vh] overflow-hidden flex flex-col">
				<form onSubmit={handleSubmit} className="flex flex-col h-full">
					<DialogHeader>
						<DialogTitle>Add Contact</DialogTitle>
						<DialogDescription>
							Request to add a contact to your profile. This addition will need
							approval before it appears on your profile.
						</DialogDescription>
					</DialogHeader>
					<div className="py-4 overflow-y-auto flex-1">
						<Alert className="mb-4">
							<AlertCircle className="h-4 w-4" />
							<AlertTitle>Approval Required</AlertTitle>
							<AlertDescription>
								This addition will be reviewed by your reporting manager before
								being updated in your profile.
							</AlertDescription>
						</Alert>
						<div className="space-y-4">
							<div>
								<Label
									htmlFor="type"
									className="block text-sm font-medium mb-1"
								>
									Contact Type
								</Label>
								<Select
									value={contactData.type}
									onValueChange={(value) => handleSelectChange('type', value)}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select contact type" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="emergency">Emergency</SelectItem>
										<SelectItem value="reference">Reference</SelectItem>
									</SelectContent>
								</Select>
							</div>
							<div>
								<Label
									htmlFor="name"
									className="block text-sm font-medium mb-1"
								>
									Contact Name
								</Label>
								<Input
									id="name"
									name="name"
									value={contactData.name}
									onChange={handleChange}
									placeholder="Enter contact name"
									required
								/>
							</div>
							<div>
								<Label
									htmlFor="relationship"
									className="block text-sm font-medium mb-1"
								>
									Relationship
								</Label>
								<Select
									value={contactData.relationship}
									onValueChange={(value) =>
										handleSelectChange('relationship', value)
									}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select relationship" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="Spouse">Spouse</SelectItem>
										<SelectItem value="Parent">Parent</SelectItem>
										<SelectItem value="Child">Child</SelectItem>
										<SelectItem value="Sibling">Sibling</SelectItem>
										<SelectItem value="Friend">Friend</SelectItem>
										<SelectItem value="Other">Other</SelectItem>
									</SelectContent>
								</Select>
							</div>
							<div className="grid grid-cols-3 gap-2">
								<div>
									<Label
										htmlFor="countryDialCode"
										className="block text-sm font-medium mb-1"
									>
										Country Code
									</Label>
									<Input
										id="countryDialCode"
										name="countryDialCode"
										value={contactData.countryDialCode}
										onChange={handleChange}
										placeholder="+65"
										required
									/>
								</div>
								<div className="col-span-2">
									<Label
										htmlFor="phone"
										className="block text-sm font-medium mb-1"
									>
										Phone Number
									</Label>
									<Input
										id="phone"
										name="phone"
										value={contactData.phone}
										onChange={handleChange}
										placeholder="Enter phone number"
										required
									/>
								</div>
							</div>
							<div>
								<Label
									htmlFor="email"
									className="block text-sm font-medium mb-1"
								>
									Email
								</Label>
								<Input
									id="email"
									name="email"
									type="email"
									value={contactData.email}
									onChange={handleChange}
									placeholder="Enter email address"
									required
								/>
							</div>
							<div>
								<Label
									htmlFor="reason"
									className="block text-sm font-medium mb-1"
								>
									Reason for Addition
								</Label>
								<Textarea
									id="reason"
									name="reason"
									placeholder="Please provide a reason for this addition..."
									className="min-h-[80px]"
									value={contactData.reason}
									onChange={handleChange}
									required
								/>
							</div>
						</div>
					</div>
					<DialogFooter>
						<Button
							type="button"
							variant="outline"
							onClick={() => setOpen(false)}
						>
							Cancel
						</Button>
						<Button type="submit">Submit Request</Button>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
}
