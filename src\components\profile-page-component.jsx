'use client';

import { DialogTrigger } from './ui/dialog';

import { Avatar, AvatarFallback, AvatarImage } from './ui/avatar';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from './ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from './ui/tabs';
import { Input } from './ui/input';
import { Label } from './ui/label';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from './ui/select';
import {
	User,
	Briefcase,
	GraduationCap,
	Users,
	Phone,
	Mail,
	MapPin,
	Calendar,
	Heart,
	Baby,
	Building,
	Award,
	Pencil,
	AlertCircle,
	CheckCircle2,
	Clock,
	Plus,
	ClipboardList,
	X,
	ChevronDown,
	ChevronUp,
} from 'lucide-react';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from './ui/dialog';
import { Textarea } from './ui/textarea';
import { useState, useEffect } from 'react';
import { Alert, AlertDescription, AlertTitle } from './ui/alert';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { getEditRequestSchema } from '@/lib/schemas/editRequestSchema';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from './ui/form';
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from './ui/collapsible';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	fetchEditRequests,
	addEditRequest,
	deleteEditRequests,
	updateEditRequest,
} from '@/lib/features/employees/editRequestSlice';
import { religions } from '@/data/religions';
import {
	findPendingRequest,
	detectChanges,
	formatChangesForDisplay,
	hasChanges,
	prepareSubmissionData,
	getSectionData as getUtilSectionData,
	addCalculatedAges,
} from '@/lib/utils/editRequestUtils';

// Helper function to format dates
const formatDate = (dateString) => {
	if (!dateString) return 'N/A';
	const date = new Date(dateString);
	return date.toLocaleDateString('en-US', {
		year: 'numeric',
		month: 'long',
		day: 'numeric',
	});
};

// Helper function to calculate duration
const calculateDuration = (startDate, endDate) => {
	const start = new Date(startDate);
	const end = new Date(endDate);
	const yearDiff = end.getFullYear() - start.getFullYear();
	const monthDiff = end.getMonth() - start.getMonth();

	let years = yearDiff;
	let months = monthDiff;

	if (monthDiff < 0) {
		years--;
		months += 12;
	}

	return `${years} years${months > 0 ? `, ${months} months` : ''}`;
};

// Helper function to capitalize words
const capitalize = (str) => {
	if (!str) return '';
	return str
		.split(' ')
		.map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
		.join(' ');
};

// Helper function to format time ago
const timeAgo = (dateString) => {
	const date = new Date(dateString);
	const now = new Date();
	const seconds = Math.floor((now - date) / 1000);

	let interval = Math.floor(seconds / 31536000);
	if (interval >= 1) {
		return interval === 1 ? '1 year ago' : `${interval} years ago`;
	}

	interval = Math.floor(seconds / 2592000);
	if (interval >= 1) {
		return interval === 1 ? '1 month ago' : `${interval} months ago`;
	}

	interval = Math.floor(seconds / 86400);
	if (interval >= 1) {
		return interval === 1 ? '1 day ago' : `${interval} days ago`;
	}

	interval = Math.floor(seconds / 3600);
	if (interval >= 1) {
		return interval === 1 ? '1 hour ago' : `${interval} hours ago`;
	}

	interval = Math.floor(seconds / 60);
	if (interval >= 1) {
		return interval === 1 ? '1 minute ago' : `${interval} minutes ago`;
	}

	return 'Just now';
};

export function ProfilePageComponent() {
	// Get user profile data from Redux store
	const dispatch = useAppDispatch();
	const { userProfile, isLoading } = useAppSelector((state) => state.employee);
	const { editRequests, isLoading: editRequestsLoading } = useAppSelector(
		(state) => state.editRequest
	);
	const { authenticatedUser } = useAppSelector((state) => state.auth);

	// React hooks must be called before any early returns
	const [notifications, setNotifications] = useState([]);
	const [showPendingRequests, setShowPendingRequests] = useState(false);
	const [cancelRequestId, setCancelRequestId] = useState(null);
	const [editingSection, setEditingSection] = useState(null);
	const [showAddChildDialog, setShowAddChildDialog] = useState(false);
	const [pendingRequestConfirmation, setPendingRequestConfirmation] = useState(null);

	// Extract data from userProfile or use fallback values
	const data = userProfile || {};
	const {
		personalDetails = {},
		education = [],
		family = {},
		experience = [],
		contact = [],
	} = data;

	// Get pending edit requests for the current user
	const pendingChanges = editRequests || [];

	// Fetch edit requests for the current user on component mount
	useEffect(() => {
		if (authenticatedUser?.userId) {
			dispatch(
				fetchEditRequests({
					// userId: authenticatedUser.userId,
					// status: 'pending', // Only fetch pending requests for the profile page
				})
			);
		}
	}, [dispatch, authenticatedUser?.userId]);

	// Helper function to format data for display
	const formatDataForDisplay = (data) => {
		if (!data || typeof data !== 'object') return data || 'N/A';

		// If it's an object with a single key-value pair, return just the value
		const keys = Object.keys(data);
		if (keys.length === 1) {
			return data[keys[0]] || 'N/A';
		}

		// For complex objects, format as key: value pairs
		return keys.map((key) => `${key}: ${data[key]}`).join(', ');
	};

	// Show loading state
	if (isLoading) {
		return (
			<div className="container mx-auto p-4 flex items-center justify-center min-h-[400px]">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
					<p className="text-muted-foreground">Loading profile...</p>
				</div>
			</div>
		);
	}

	// Show message if no profile data
	if (!userProfile) {
		return (
			<div className="container mx-auto p-4 flex items-center justify-center min-h-[400px]">
				<div className="text-center">
					<p className="text-muted-foreground">No profile data available.</p>
				</div>
			</div>
		);
	}

	const addNotification = (message, type = 'success') => {
		const id = Date.now();
		setNotifications((prev) => [...prev, { id, message, type }]);
		setTimeout(() => {
			setNotifications((prev) =>
				prev.filter((notification) => notification.id !== id)
			);
		}, 5000);
	};

	// Helper function to filter oldData to only include fields present in newData
	const filterOldDataByNewData = (oldData, newData) => {
		if (!oldData || !newData || typeof newData !== 'object') {
			return {};
		}

		const filteredOldData = {};

		// Recursively filter based on newData structure
		const filterRecursive = (oldObj, newObj, targetObj) => {
			if (!oldObj || !newObj || typeof newObj !== 'object') {
				return;
			}

			Object.keys(newObj).forEach(key => {
				if (Array.isArray(newObj[key])) {
					// For arrays, include the corresponding old array (or empty array if not exists)
					targetObj[key] = Array.isArray(oldObj[key]) ? oldObj[key] : [];
				} else if (typeof newObj[key] === 'object' && newObj[key] !== null) {
					// For nested objects, recursively filter
					targetObj[key] = {};
					filterRecursive(oldObj[key], newObj[key], targetObj[key]);
				} else {
					// For primitive values, include the old value if it exists
					targetObj[key] = oldObj.hasOwnProperty(key) ? oldObj[key] : undefined;
				}
			});
		};

		filterRecursive(oldData, newData, filteredOldData);
		return filteredOldData;
	};

	const handleSectionEditRequest = (section, oldData, newData, reason) => {
		// Filter oldData to only include fields present in newData
		const filteredOldData = filterOldDataByNewData(oldData, newData);

		// First, check if there are any actual changes
		if (!hasChanges(filteredOldData, newData)) {
			addNotification(
				'No changes detected. Please modify at least one field before submitting.',
				'error'
			);
			return;
		}

		// Check for existing pending request
		const pendingRequest = findPendingRequest(
			editRequests,
			authenticatedUser?.userId,
			section.toLowerCase().replace(' ', '-')
		);

		if (pendingRequest) {
			// Check if the new changes are different from the pending request
			if (!hasChanges(pendingRequest.newData, newData)) {
				addNotification(
					'No new changes detected. Your pending request already contains these values.',
					'error'
				);
				return;
			}

			// Show confirmation dialog for existing pending request
			const changes = detectChanges(pendingRequest.newData, newData);
			setPendingRequestConfirmation({
				section,
				oldData: filteredOldData, // Use filtered oldData
				newData,
				reason,
				pendingRequest,
				changes,
			});
			return;
		}

		// No pending request, create new one
		const editRequestData = {
			userId: authenticatedUser?.userId,
			section: `${section.toLowerCase().replace(' ', '-')}-details`, // Convert to API format
			oldData: filteredOldData, // Use filtered oldData
			newData,
			reason,
		};

		dispatch(addEditRequest(editRequestData));
		addNotification(
			`Your request to update ${section} has been submitted for review.`
		);
	};

	const handleConfirmPendingRequest = () => {
		if (!pendingRequestConfirmation) return;

		const { section, oldData, newData, reason, pendingRequest } = pendingRequestConfirmation;

		// Double-check that there are still changes (safety check)
		if (!hasChanges(pendingRequest.newData, newData)) {
			addNotification(
				'No new changes detected. Your pending request already contains these values.',
				'error'
			);
			setPendingRequestConfirmation(null);
			return;
		}

		// Update the existing pending request
		// Note: oldData is already filtered when passed to pendingRequestConfirmation
		const updatedRequestData = {
			id: pendingRequest._id,
			oldData, // This is already filtered oldData
			newData,
			reason,
		};

		dispatch(updateEditRequest(updatedRequestData));
		addNotification(
			`Your pending request for ${section} has been updated.`
		);

		setPendingRequestConfirmation(null);
	};

	const handleAddChildRequest = (childData, reason) => {
		// Get current family data
		const currentFamilyData = getSectionData('family');

		// Add the new child to the children array with calculated age
		const updatedChildren = [
			...(currentFamilyData.children || []),
			{
				...childData,
				age: Math.floor((new Date() - new Date(childData.dob)) / (365.25 * 24 * 60 * 60 * 1000))
			}
		];

		// Create family edit request with the new child
		const newFamilyData = {
			...currentFamilyData,
			children: updatedChildren,
		};

		// Use the existing section edit request handler
		// The filtering will happen inside handleSectionEditRequest
		handleSectionEditRequest('family', currentFamilyData, newFamilyData, reason);
		setShowAddChildDialog(false);
	};

	const handleCancelRequest = (id) => {
		// Delete edit request using Redux action
		dispatch(deleteEditRequests([id]));
		setCancelRequestId(null);
		addNotification('Your request has been cancelled.', 'success');
	};

	// Helper function to get section data
	const getSectionData = (section) => {
		switch (section) {
			case 'personal':
				return personalDetails;
			case 'family':
				return family;
			case 'education':
				return education;
			case 'experience':
				return experience;
			case 'contact':
				return contact;
			default:
				return {};
		}
	};

	// Helper function to format date for display
	const formatDate = (dateString) => {
		if (!dateString) return 'N/A';
		try {
			return new Date(dateString).toLocaleDateString('en-US', {
				year: 'numeric',
				month: 'short',
				day: 'numeric',
			});
		} catch (error) {
			return 'Invalid Date';
		}
	};

	// Helper function to capitalize strings
	const capitalize = (str) => {
		if (!str) return '';
		return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
	};

	return (
		<div className="container mx-auto p-4">
			{/* Hero Section with Profile Overview */}
			<div className="relative mb-8 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl overflow-hidden">
				<div className="absolute inset-0 bg-black opacity-10 z-0"></div>
				<div className="relative z-10 p-8 flex flex-col md:flex-row items-center gap-6 text-white">
					<div className="relative">
						<Avatar className="h-32 w-32 border-4 border-white shadow-lg">
							<AvatarImage
								src={personalDetails?.profilePhoto || '/placeholder.svg'}
								alt={personalDetails?.nameOnNRIC || 'User'}
							/>
							<AvatarFallback className="text-3xl bg-indigo-700">
								{personalDetails?.nameOnNRIC
									? personalDetails.nameOnNRIC
											.split(' ')
											.map((n) => n[0])
											.join('')
									: 'U'}
							</AvatarFallback>
						</Avatar>
						<Badge className="absolute -bottom-2 right-0 bg-green-500 border-2 border-white">
							Active
						</Badge>
					</div>

					<div className="text-center md:text-left">
						<h1 className="text-3xl font-bold">
							{personalDetails?.nameOnNRIC || 'User Name'}
						</h1>
						<div className="flex flex-wrap justify-center md:justify-start gap-2 mt-2">
							<Badge
								variant="outline"
								className="bg-white/20 text-white border-white/40"
							>
								ID: {personalDetails?.employeeOrgId || 'N/A'}
							</Badge>
							<Badge
								variant="outline"
								className="bg-white/20 text-white border-white/40"
							>
								{experience && experience.length > 0
									? experience[0]?.designation
									: 'N/A'}
							</Badge>
							<Badge
								variant="outline"
								className="bg-white/20 text-white border-white/40"
							>
								{capitalize(personalDetails?.nationality || 'N/A')}
							</Badge>
						</div>
						<div className="mt-4 flex flex-wrap justify-center md:justify-start gap-4">
							<div className="flex items-center gap-1">
								<Mail className="h-4 w-4" />
								<span>{data?.email || 'N/A'}</span>
							</div>
							<div className="flex items-center gap-1">
								<Phone className="h-4 w-4" />
								<span>
									{personalDetails?.countryDialCode || ''}{' '}
									{personalDetails?.mobile || 'N/A'}
								</span>
							</div>
							<div className="flex items-center gap-1">
								<Calendar className="h-4 w-4" />
								<span>
									Joined: {formatDate(personalDetails?.dateOfJoining)}
								</span>
							</div>
						</div>
					</div>
				</div>
			</div>

			{/* Notifications */}
			<div className="fixed top-4 right-4 z-50 space-y-2 max-w-md">
				{notifications.map((notification) => (
					<Alert
						key={notification.id}
						className={`border-${
							notification.type === 'success'
								? 'green'
								: notification.type === 'error'
									? 'red'
									: 'amber'
						}-500 bg-${
							notification.type === 'success'
								? 'green'
								: notification.type === 'error'
									? 'red'
									: 'amber'
						}-50 shadow-md transition-all duration-300 ease-in-out`}
					>
						{notification.type === 'success' ? (
							<CheckCircle2 className="h-5 w-5 text-green-600" />
						) : notification.type === 'error' ? (
							<AlertCircle className="h-5 w-5 text-red-600" />
						) : (
							<AlertCircle className="h-5 w-5 text-amber-600" />
						)}
						<AlertDescription
							className={`text-${
								notification.type === 'success'
									? 'green'
									: notification.type === 'error'
										? 'red'
										: 'amber'
							}-700`}
						>
							{notification.message}
						</AlertDescription>
					</Alert>
				))}
			</div>

			{/* Pending Changes Section */}
			{pendingChanges.length > 0 && (
				<Collapsible
					open={showPendingRequests}
					onOpenChange={setShowPendingRequests}
					className="mb-6 border rounded-lg overflow-hidden"
				>
					<div className="bg-amber-50 border-b border-amber-200">
						<CollapsibleTrigger className="flex items-center justify-between w-full p-4 text-left">
							<div className="flex items-center gap-2">
								<Clock className="h-5 w-5 text-amber-600" />
								<div>
									<h3 className="font-medium text-amber-800">
										Pending Change Requests
									</h3>
									<p className="text-sm text-amber-700">
										You have {pendingChanges.length} pending change request
										{pendingChanges.length > 1 ? 's' : ''}
									</p>
								</div>
							</div>
							<div className="flex items-center gap-2">
								<Badge className="bg-amber-100 text-amber-800 hover:bg-amber-200">
									{pendingChanges.length} Pending
								</Badge>
								{showPendingRequests ? (
									<ChevronUp className="h-5 w-5 text-amber-600" />
								) : (
									<ChevronDown className="h-5 w-5 text-amber-600" />
								)}
							</div>
						</CollapsibleTrigger>
					</div>
					<CollapsibleContent>
						<div className="p-4 bg-white">
							<div className="space-y-4">
								{pendingChanges.map((change) => (
									<div
										key={change._id}
										className="border rounded-md p-4 bg-slate-50"
									>
										<div className="flex justify-between items-start">
											<div>
												<div className="flex items-center gap-2">
													<Badge
														className={
															change.status === 'pending'
																? 'bg-amber-100 text-amber-800'
																: change.status === 'approved'
																	? 'bg-green-100 text-green-800'
																	: 'bg-red-100 text-red-800'
														}
													>
														{change.status === 'pending'
															? 'Pending Approval'
															: change.status === 'approved'
																? 'Approved'
																: 'Rejected'}
													</Badge>
													<span className="text-sm text-muted-foreground">
														{timeAgo(change.submittedAt)}
													</span>
												</div>
												<h4 className="font-medium mt-2">
													{capitalize(change.section.replace('-', ' '))} -{' '}
													{change.field || 'Field Update'}
												</h4>
												<div className="mt-1 grid grid-cols-2 gap-4">
													<div>
														<p className="text-xs text-muted-foreground">
															Current Value
														</p>
														<p className="text-sm">
															{formatDataForDisplay(change.oldData)}
														</p>
													</div>
													<div>
														<p className="text-xs text-muted-foreground">
															Requested Value
														</p>
														<p className="text-sm font-medium">
															{formatDataForDisplay(change.newData)}
														</p>
													</div>
												</div>
												{change.reason && (
													<div className="mt-2">
														<p className="text-xs text-muted-foreground">
															Reason
														</p>
														<p className="text-sm">{change.reason}</p>
													</div>
												)}
											</div>
											{change.status === 'pending' && (
												<Button
													variant="ghost"
													size="icon"
													className="h-8 w-8"
													onClick={() => setCancelRequestId(change._id)}
												>
													<X className="h-4 w-4" />
												</Button>
											)}
										</div>
									</div>
								))}
							</div>
						</div>
					</CollapsibleContent>
				</Collapsible>
			)}

			{/* Main Content with Tabs */}
			<Tabs defaultValue="personal" className="w-full">
				<TabsList className="grid grid-cols-5 mb-8">
					<TabsTrigger value="personal" className="flex items-center gap-2">
						<User className="h-4 w-4" />
						<span className="hidden sm:inline">Personal</span>
					</TabsTrigger>
					<TabsTrigger value="family" className="flex items-center gap-2">
						<Users className="h-4 w-4" />
						<span className="hidden sm:inline">Family</span>
					</TabsTrigger>
					<TabsTrigger value="education" className="flex items-center gap-2">
						<GraduationCap className="h-4 w-4" />
						<span className="hidden sm:inline">Education</span>
					</TabsTrigger>
					<TabsTrigger value="experience" className="flex items-center gap-2">
						<Briefcase className="h-4 w-4" />
						<span className="hidden sm:inline">Experience</span>
					</TabsTrigger>
					<TabsTrigger value="contact" className="flex items-center gap-2">
						<Phone className="h-4 w-4" />
						<span className="hidden sm:inline">Contact</span>
					</TabsTrigger>
				</TabsList>

				{/* Personal Details Tab */}
				<TabsContent value="personal">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<Card className="overflow-hidden">
							<CardHeader className="bg-slate-50">
								<div className="flex justify-between items-center">
									<div>
										<CardTitle className="flex items-center gap-2">
											<User className="h-5 w-5 text-indigo-600" />
											Personal Information
										</CardTitle>
										<CardDescription>
											Your personal information. Click edit to request changes.
										</CardDescription>
									</div>
									<Button
										variant="outline"
										size="sm"
										onClick={() => setEditingSection('personal')}
										className="flex items-center self-start gap-2"
									>
										<Pencil className="h-4 w-4" />
										Edit Section
									</Button>
								</div>
							</CardHeader>
							<CardContent className="pt-6">
								<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
									<div>
										<p className="text-sm text-muted-foreground">Full Name</p>
										<p className="font-medium">
											{personalDetails?.nameOnNRIC || 'N/A'}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">Employee ID</p>
										<p className="font-medium">
											{personalDetails?.employeeOrgId || 'N/A'}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">
											Date of Birth
										</p>
										<p className="font-medium">
											{formatDate(personalDetails?.dob)}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">Age</p>
										<p className="font-medium">
											{personalDetails?.age
												? `${personalDetails.age} years`
												: 'N/A'}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">Gender</p>
										<p className="font-medium">
											{capitalize(personalDetails?.gender || 'N/A')}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">
											IC/FIN Number
										</p>
										<p className="font-medium">
											{personalDetails?.icFinNumber || 'N/A'}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">Religion</p>
										<p className="font-medium">
											{capitalize(personalDetails?.religion || 'N/A')}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">Race</p>
										<p className="font-medium">
											{capitalize(personalDetails?.race || 'N/A')}
										</p>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card className="overflow-hidden">
							<CardHeader className="bg-slate-50">
								<CardTitle className="flex items-center gap-2">
									<MapPin className="h-5 w-5 text-indigo-600" />
									Address & Citizenship
								</CardTitle>
								<CardDescription>
									Address and citizenship details cannot be edited. Please
									contact HR for any corrections.
								</CardDescription>
							</CardHeader>
							<CardContent className="pt-6">
								<div className="mb-6">
									<p className="text-sm text-muted-foreground">Address</p>
									<p className="font-medium whitespace-pre-line">
										{personalDetails?.address || 'N/A'}
									</p>
								</div>

								<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
									<div>
										<p className="text-sm text-muted-foreground">Postal Code</p>
										<p className="font-medium">
											{personalDetails?.postalCode || 'N/A'}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">Street Name</p>
										<p className="font-medium">
											{personalDetails?.streetName || 'N/A'}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">Nationality</p>
										<p className="font-medium">
											{capitalize(personalDetails?.nationality || 'N/A')}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">
											Residential Status
										</p>
										<p className="font-medium">
											{personalDetails?.residentialStatus || 'N/A'}
										</p>
									</div>
								</div>
							</CardContent>
						</Card>
					</div>
				</TabsContent>

				{/* Education Tab */}
				<TabsContent value="education">
					<Card>
						<CardHeader className="bg-slate-50">
							<div className="flex justify-between items-center">
								<div>
									<CardTitle className="flex items-center gap-2">
										<GraduationCap className="h-5 w-5 text-indigo-600" />
										Educational Background
									</CardTitle>
									<CardDescription>
										Your educational history. Click edit to request changes.
									</CardDescription>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => setEditingSection('education')}
									className="flex items-center self-start gap-2"
								>
									<Pencil className="h-4 w-4" />
									Edit Section
								</Button>
							</div>
						</CardHeader>
						<CardContent className="pt-6">
							{education && education.length > 0 ? (
								<div className="space-y-6">
									{education.map((edu) => (
										<div
											key={edu._id}
											className="relative pl-8 border-l-2 border-indigo-200 pb-6"
										>
											<div className="absolute -left-3 top-0 bg-indigo-600 rounded-full p-1">
												<GraduationCap className="h-4 w-4 text-white" />
											</div>
											<div className="mb-2">
												<Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">
													{formatDate(edu.startDate)} -{' '}
													{formatDate(edu.endDate)}
												</Badge>
												<Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-200">
													{calculateDuration(edu.startDate, edu.endDate)}
												</Badge>
											</div>
											<h3 className="text-xl font-bold">{edu.instituteName}</h3>
											<p className="text-muted-foreground">
												{edu.qualification.replace('_', ' ')}
											</p>
											<div className="mt-2 flex items-center">
												<Award className="h-4 w-4 text-amber-500 mr-1" />
												<span className="font-medium">Grade: {edu.grade}</span>
											</div>
										</div>
									))}
								</div>
							) : (
								<p className="text-muted-foreground">
									No education information available.
								</p>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				{/* Experience Tab */}
				<TabsContent value="experience">
					<Card>
						<CardHeader className="bg-slate-50">
							<div className="flex justify-between items-center">
								<div>
									<CardTitle className="flex items-center gap-2">
										<Briefcase className="h-5 w-5 text-indigo-600" />
										Professional Experience
									</CardTitle>
									<CardDescription>
										Your work experience. Click edit to request changes.
									</CardDescription>
								</div>
								<Button
									variant="outline"
									size="sm"
									onClick={() => setEditingSection('experience')}
									className="flex items-center self-start gap-2"
								>
									<Pencil className="h-4 w-4" />
									Edit Section
								</Button>
							</div>
						</CardHeader>
						<CardContent className="pt-6">
							{experience && experience.length > 0 ? (
								<div className="space-y-6">
									{experience.map((exp) => (
										<div
											key={exp._id}
											className="relative pl-8 border-l-2 border-indigo-200 pb-6"
										>
											<div className="absolute -left-3 top-0 bg-indigo-600 rounded-full p-1">
												<Building className="h-4 w-4 text-white" />
											</div>
											<div className="mb-2">
												<Badge className="bg-indigo-100 text-indigo-800 hover:bg-indigo-200">
													{formatDate(exp.periodFrom)} -{' '}
													{formatDate(exp.periodTo)}
												</Badge>
												<Badge className="ml-2 bg-green-100 text-green-800 hover:bg-green-200">
													{calculateDuration(exp.periodFrom, exp.periodTo)}
												</Badge>
											</div>
											<h3 className="text-xl font-bold">{exp.companyName}</h3>
											<p className="text-lg font-medium">{exp.designation}</p>
											<div className="mt-2 flex items-center">
												<MapPin className="h-4 w-4 text-rose-500 mr-1" />
												<span>{exp.location}</span>
											</div>
											{exp.reasonForLeaving && (
												<div className="mt-2">
													<span className="text-sm text-muted-foreground">
														Reason for leaving: {exp.reasonForLeaving}
													</span>
												</div>
											)}
										</div>
									))}
								</div>
							) : (
								<p className="text-muted-foreground">
									No experience information available.
								</p>
							)}
						</CardContent>
					</Card>
				</TabsContent>

				{/* Family Tab */}
				<TabsContent value="family">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<Card className="overflow-hidden">
							<CardHeader className="bg-slate-50">
								<div className="flex justify-between items-center">
									<div>
										<CardTitle className="flex items-center gap-2">
											<Heart className="h-5 w-5 text-rose-500" />
											Marital Status
										</CardTitle>
										<CardDescription>
											Your family information. Click edit to request changes.
										</CardDescription>
									</div>
									<Button
										variant="outline"
										size="sm"
										onClick={() => setEditingSection('family')}
										className="flex items-center self-start gap-2"
									>
										<Pencil className="h-4 w-4" />
										Edit Section
									</Button>
								</div>
							</CardHeader>
							<CardContent className="pt-6">
								<div className="grid gap-4">
									<div>
										<p className="text-sm text-muted-foreground">
											Marital Status
										</p>
										<p className="font-medium">
											{capitalize(family?.maritalStatus || 'N/A')}
										</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">Spouse Name</p>
										<p className="font-medium">{family?.spouseName || 'N/A'}</p>
									</div>
									<div>
										<p className="text-sm text-muted-foreground">
											Spouse Employment Status
										</p>
										<p className="font-medium">
											{capitalize(family?.spouseEmploymentStatus || 'N/A')}
										</p>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card className="overflow-hidden">
							<CardHeader className="bg-slate-50">
								<div className="flex justify-between items-center">
									<div>
										<CardTitle className="flex items-center gap-2">
											<Baby className="h-5 w-5 text-blue-500" />
											Children
										</CardTitle>
										<CardDescription>
											Manage your children information. Click add to request adding a new child.
										</CardDescription>
									</div>
									<Button
										variant="outline"
										size="sm"
										onClick={() => setShowAddChildDialog(true)}
										className="flex items-center self-start gap-2"
									>
										<Plus className="h-4 w-4" />
										Add Child
									</Button>
								</div>
							</CardHeader>
							<CardContent className="pt-6">
								{family?.children && family.children.length > 0 ? (
									<div className="space-y-4">
										{family.children.map((child, index) => (
											<div
												key={index}
												className="p-4 rounded-lg border bg-slate-50"
											>
												<div className="flex justify-between items-start">
													<div>
														<h4 className="font-semibold">{child.name}</h4>
														<p className="text-sm text-muted-foreground">
															Age: {child.age} years
														</p>
														<p className="text-sm text-muted-foreground mt-1">
															Nationality: {capitalize(child.nationality)}
														</p>
													</div>
													<Badge className="bg-blue-100 text-blue-800">
														DOB: {formatDate(child.dob)}
													</Badge>
												</div>
											</div>
										))}
									</div>
								) : (
									<p className="text-muted-foreground">
										No children information available.
									</p>
								)}
							</CardContent>
						</Card>
					</div>
				</TabsContent>

				{/* Contact Tab */}
				<TabsContent value="contact">
					<Card>
						<CardHeader className="bg-slate-50">
							<div className="flex justify-between items-center">
								<div>
									<CardTitle className="flex items-center gap-2">
										<Phone className="h-5 w-5 text-indigo-600" />
										Contact Information
									</CardTitle>
									<CardDescription>
										Your emergency contact information. Click edit to request
										changes.
									</CardDescription>
								</div>
								<div className="flex items-center gap-2">
									<Button
										variant="outline"
										size="sm"
										onClick={() => setEditingSection('contact')}
										className="flex items-center self-start gap-2"
									>
										<Pencil className="h-4 w-4" />
										Edit Section
									</Button>
								</div>
							</div>
						</CardHeader>
						<CardContent className="pt-6">
							{contact && contact.length > 0 ? (
								<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
									{contact.map((cont) => (
										<div
											key={cont._id}
											className="p-4 rounded-lg border bg-slate-50"
										>
											<h3 className="font-semibold text-lg mb-4">
												{cont.name}
											</h3>
											<div className="space-y-3">
												<Badge className="bg-indigo-100 text-indigo-800">
													{cont.relationship}
												</Badge>
												<div className="flex items-center gap-2">
													<Phone className="h-4 w-4 text-muted-foreground" />
													<span>
														{cont.countryDialCode} {cont.phone}
													</span>
												</div>
												<div className="flex items-center gap-2">
													<Mail className="h-4 w-4 text-muted-foreground" />
													<span>{cont.email}</span>
												</div>
												<div className="flex items-center gap-2">
													<ClipboardList className="h-4 w-4 text-muted-foreground" />
													<span>{capitalize(cont.type)}</span>
												</div>
											</div>
										</div>
									))}
								</div>
							) : (
								<p className="text-muted-foreground">
									No contact information available.
								</p>
							)}
						</CardContent>
					</Card>
				</TabsContent>
			</Tabs>
			{/* Confirmation Dialog for Cancelling Request */}
			<Dialog
				open={cancelRequestId !== null}
				onOpenChange={(open) => !open && setCancelRequestId(null)}
			>
				<DialogContent className="max-w-md">
					<DialogHeader>
						<DialogTitle>Cancel Request</DialogTitle>
						<DialogDescription>
							Are you sure you want to cancel this request? This action cannot
							be undone.
						</DialogDescription>
					</DialogHeader>
					<div className="py-4">
						{cancelRequestId && (
							<div className="p-3 bg-slate-50 rounded-md border">
								<p className="font-medium">
									{capitalize(
										pendingChanges
											.find((c) => c._id === cancelRequestId)
											?.section?.replace('-', ' ') || 'Unknown'
									)}{' '}
									-{' '}
									{pendingChanges.find((c) => c._id === cancelRequestId)
										?.field || 'Field Update'}
								</p>
								<div className="mt-1 text-sm text-muted-foreground">
									<p>
										Requested change:{' '}
										{formatDataForDisplay(
											pendingChanges.find((c) => c._id === cancelRequestId)
												?.newData
										)}
									</p>
									<p className="mt-1">
										Submitted{' '}
										{timeAgo(
											pendingChanges.find((c) => c._id === cancelRequestId)
												?.submittedAt
										)}
									</p>
								</div>
							</div>
						)}
					</div>
					<DialogFooter>
						<Button variant="outline" onClick={() => setCancelRequestId(null)}>
							Keep Request
						</Button>
						<Button
							variant="destructive"
							onClick={() => handleCancelRequest(cancelRequestId)}
						>
							Cancel Request
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Section Edit Dialog */}
			<SectionEditDialog
				section={editingSection}
				currentData={getSectionData(editingSection)}
				onSubmit={handleSectionEditRequest}
				onClose={() => setEditingSection(null)}
			/>

			{/* Add Child Dialog */}
			<AddChildDialog
				open={showAddChildDialog}
				onSubmit={handleAddChildRequest}
				onClose={() => setShowAddChildDialog(false)}
			/>

			{/* Pending Request Confirmation Dialog */}
			<Dialog
				open={!!pendingRequestConfirmation}
				onOpenChange={() => setPendingRequestConfirmation(null)}
			>
				<DialogContent className="max-w-md">
					<DialogHeader>
						<DialogTitle className="flex items-center gap-2">
							<AlertCircle className="h-5 w-5 text-amber-500" />
							Pending Request Exists
						</DialogTitle>
						<DialogDescription>
							You already have a pending request for this section. Would you like to update it with your new changes?
						</DialogDescription>
					</DialogHeader>

					{pendingRequestConfirmation && (
						<div className="space-y-4">
							<div>
								<h4 className="font-medium text-sm">Section:</h4>
								<p className="text-sm text-muted-foreground capitalize">
									{pendingRequestConfirmation.section}
								</p>
							</div>

							<div>
								<h4 className="font-medium text-sm">Changes:</h4>
								<p className="text-sm text-muted-foreground">
									{formatChangesForDisplay(pendingRequestConfirmation.changes)}
								</p>
							</div>
						</div>
					)}

					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setPendingRequestConfirmation(null)}
						>
							Cancel
						</Button>
						<Button onClick={handleConfirmPendingRequest}>
							Update Request
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}

function SectionEditDialog({ section, currentData, onSubmit, onClose }) {
	// Get the appropriate schema for the section
	const schema = getEditRequestSchema(section);

	// Helper function to get default values based on section and current data
	const getDefaultValues = (section, currentData) => {
		if (!currentData) return { reason: '' };

		// Handle different data structures for different sections
		if (section === 'contact') {
			return {
				reason: '',
				contact: Array.isArray(currentData) ? currentData : (currentData.contact || []),
			};
		} else if (section === 'education') {
			return {
				reason: '',
				education: Array.isArray(currentData) ? currentData : (currentData.education || []),
			};
		} else if (section === 'experience') {
			return {
				reason: '',
				experience: Array.isArray(currentData) ? currentData : (currentData.experience || []),
			};
		} else if (section === 'family') {
			return {
				reason: '',
				maritalStatus: currentData.maritalStatus || '',
				spouseName: currentData.spouseName || '',
				spouseEmploymentStatus: currentData.spouseEmploymentStatus || '',
				children: addCalculatedAges(currentData.children || []),
			};
		} else {
			// For personal section
			return {
				reason: '',
				...currentData,
				dob: currentData.dob ? currentData.dob.split('T')[0] : '',
			};
		}
	};

	// Initialize form with react-hook-form and Zod validation
	const form = useForm({
		resolver: zodResolver(schema),
		defaultValues: getDefaultValues(section, currentData),
	});

	// Reset form when section or currentData changes
	useEffect(() => {
		if (section && currentData) {
			form.reset(getDefaultValues(section, currentData));
		}
	}, [section, currentData, form]);

	// Handle form submission
	const handleFormSubmit = (data) => {
		const { reason, ...formData } = data;
		onSubmit(section, currentData, formData, reason);
		onClose();
		form.reset();
	};

	const renderFields = () => {
		switch (section) {
			case 'personal':
				return (
					<div className="space-y-6">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="nameOnNRIC"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											Full Name
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder="Enter full name"
												className="w-full"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="dob"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											Date of Birth
										</FormLabel>
										<FormControl>
											<Input {...field} type="date" className="w-full" />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="gender"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											Gender
										</FormLabel>
										<Select onValueChange={field.onChange} value={field.value}>
											<FormControl>
												<SelectTrigger className="w-full">
													<SelectValue placeholder="Select gender" />
												</SelectTrigger>
											</FormControl>
											<SelectContent className="z-50">
												<SelectItem value="male">Male</SelectItem>
												<SelectItem value="female">Female</SelectItem>
												<SelectItem value="other">Other</SelectItem>
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="icFinNumber"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											IC/FIN Number
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder="Enter IC/FIN number"
												className="w-full"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="religion"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											Religion
										</FormLabel>
										<Select onValueChange={field.onChange} value={field.value}>
											<FormControl>
												<SelectTrigger className="w-full">
													<SelectValue placeholder="Select religion" />
												</SelectTrigger>
											</FormControl>
											<SelectContent className="z-50">
												{religions.map((religion) => (
													<SelectItem
														key={religion.value}
														value={religion.value}
													>
														{religion.label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="race"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">Race</FormLabel>
										<Select onValueChange={field.onChange} value={field.value}>
											<FormControl>
												<SelectTrigger className="w-full">
													<SelectValue placeholder="Select race" />
												</SelectTrigger>
											</FormControl>
											<SelectContent className="z-50">
												<SelectItem value="chinese">Chinese</SelectItem>
												<SelectItem value="eurasian">Eurasian</SelectItem>
												<SelectItem value="indian">Indian</SelectItem>
												<SelectItem value="malay">Malay</SelectItem>
												<SelectItem value="prefer-not-to-contribute">
													Prefer not to contribute
												</SelectItem>
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</div>
				);
			case 'family':
				const maritalStatus = form.watch('maritalStatus');
				const showSpouseFields = maritalStatus && maritalStatus !== 'single';

				return (
					<div className="space-y-6">
						<FormField
							control={form.control}
							name="maritalStatus"
							render={({ field }) => (
								<FormItem>
									<FormLabel className="text-sm font-medium">
										Marital Status
									</FormLabel>
									<Select
										onValueChange={(value) => {
											field.onChange(value);
											// Clear spouse fields when changing to single
											if (value === 'single') {
												form.setValue('spouseName', '');
												form.setValue('spouseEmploymentStatus', '');
											}
										}}
										value={field.value}
									>
										<FormControl>
											<SelectTrigger className="w-full">
												<SelectValue placeholder="Select marital status" />
											</SelectTrigger>
										</FormControl>
										<SelectContent className="z-50">
											<SelectItem value="single">Single</SelectItem>
											<SelectItem value="married">Married</SelectItem>
											<SelectItem value="divorced">Divorced</SelectItem>
											<SelectItem value="widowed">Widowed</SelectItem>
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>

						{showSpouseFields && (
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<FormField
									control={form.control}
									name="spouseName"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-sm font-medium">
												Spouse Name
												{maritalStatus === 'married' && (
													<span className="text-red-500 ml-1">*</span>
												)}
											</FormLabel>
											<FormControl>
												<Input
													{...field}
													placeholder="Enter spouse name"
													className="w-full"
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name="spouseEmploymentStatus"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-sm font-medium">
												Spouse Employment Status
											</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value}
											>
												<FormControl>
													<SelectTrigger className="w-full">
														<SelectValue placeholder="Select employment status" />
													</SelectTrigger>
												</FormControl>
												<SelectContent className="z-50">
													<SelectItem value="employed">Employed</SelectItem>
													<SelectItem value="unemployed">Unemployed</SelectItem>
													<SelectItem value="self-employed">
														Self-Employed
													</SelectItem>
													<SelectItem value="retired">Retired</SelectItem>
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						)}

						{/* Children Section - Read Only Display */}
						<div className="space-y-4">
							<div className="flex items-center justify-between">
								<h4 className="text-sm font-medium">Children</h4>
								<p className="text-xs text-muted-foreground">
									Use &quot;Add Child&quot; button to add new children
								</p>
							</div>

							{form.watch('children')?.length > 0 ? (
								<div className="space-y-3">
									{form.watch('children').map((child, index) => (
										<div key={index} className="p-3 border rounded-md bg-slate-50">
											<div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
												<div>
													<span className="font-medium">Name:</span> {child.name}
												</div>
												<div>
													<span className="font-medium">Age:</span> {child.age} years
												</div>
												<div>
													<span className="font-medium">DOB:</span> {formatDate(child.dob)}
												</div>
												<div>
													<span className="font-medium">Nationality:</span> {capitalize(child.nationality)}
												</div>
											</div>
										</div>
									))}
								</div>
							) : (
								<p className="text-sm text-muted-foreground italic">
									No children added yet
								</p>
							)}
						</div>
					</div>
				);
			case 'contact':
				return (
					<div className="space-y-6">
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="name"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											Contact Name
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder="Enter contact name"
												className="w-full"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="relationship"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											Relationship
										</FormLabel>
										<Select onValueChange={field.onChange} value={field.value}>
											<FormControl>
												<SelectTrigger className="w-full">
													<SelectValue placeholder="Select relationship" />
												</SelectTrigger>
											</FormControl>
											<SelectContent className="z-50">
												<SelectItem value="Spouse">Spouse</SelectItem>
												<SelectItem value="Parent">Parent</SelectItem>
												<SelectItem value="Child">Child</SelectItem>
												<SelectItem value="Sibling">Sibling</SelectItem>
												<SelectItem value="Friend">Friend</SelectItem>
												<SelectItem value="Other">Other</SelectItem>
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
							<FormField
								control={form.control}
								name="countryDialCode"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											Country Code
										</FormLabel>
										<FormControl>
											<Input {...field} placeholder="+65" className="w-full" />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<div className="md:col-span-2">
								<FormField
									control={form.control}
									name="phone"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-sm font-medium">
												Phone Number
											</FormLabel>
											<FormControl>
												<Input
													{...field}
													placeholder="Enter phone number"
													className="w-full"
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</div>
						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<FormField
								control={form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											Email Address
										</FormLabel>
										<FormControl>
											<Input
												{...field}
												type="email"
												placeholder="Enter email address"
												className="w-full"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
							<FormField
								control={form.control}
								name="type"
								render={({ field }) => (
									<FormItem>
										<FormLabel className="text-sm font-medium">
											Contact Type
										</FormLabel>
										<Select onValueChange={field.onChange} value={field.value}>
											<FormControl>
												<SelectTrigger className="w-full">
													<SelectValue placeholder="Select contact type" />
												</SelectTrigger>
											</FormControl>
											<SelectContent className="z-50">
												<SelectItem value="emergency">Emergency</SelectItem>
												<SelectItem value="reference">Reference</SelectItem>
											</SelectContent>
										</Select>
										<FormMessage />
									</FormItem>
								)}
							/>
						</div>
					</div>
				);
			case 'education':
				return (
					<MultipleEntriesManager
						section="education"
						entries={form.watch('education') || []}
						onEntriesChange={(entries) => form.setValue('education', entries)}
						form={form}
					/>
				);
			case 'experience':
				return (
					<MultipleEntriesManager
						section="experience"
						entries={form.watch('experience') || []}
						onEntriesChange={(entries) => form.setValue('experience', entries)}
						form={form}
					/>
				);
			case 'contact':
				return (
					<MultipleEntriesManager
						section="contact"
						entries={form.watch('contact') || []}
						onEntriesChange={(entries) => form.setValue('contact', entries)}
						form={form}
					/>
				);
			default:
				return (
					<div className="text-center py-8">
						<p className="text-muted-foreground">
							This section is not available for editing yet.
						</p>
						<p className="text-sm text-muted-foreground mt-2">
							Please contact HR for assistance with {section} changes.
						</p>
					</div>
				);
		}
	};

	if (!section) return null;

	return (
		<Dialog open={!!section} onOpenChange={() => onClose()}>
			<DialogContent className="max-w-4xl w-[95vw] max-h-[90vh] overflow-hidden flex flex-col">
				<DialogHeader className="flex-shrink-0 pb-4 border-b">
					<DialogTitle className="text-xl font-semibold">
						Edit {capitalize(section)} Information
					</DialogTitle>
					<DialogDescription className="text-sm text-muted-foreground">
						Request changes to your {section} information. All changes require
						approval from your manager.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(handleFormSubmit)}
						className="flex-1 overflow-y-auto py-4"
					>
						<div className="space-y-6">
							<Alert className="border-amber-200 bg-amber-50">
								<AlertCircle className="h-4 w-4 text-amber-600" />
								<AlertTitle className="text-amber-800">
									Approval Required
								</AlertTitle>
								<AlertDescription className="text-amber-700">
									Changes will be reviewed by your reporting manager before
									being updated in your profile.
								</AlertDescription>
							</Alert>

							<div className="space-y-6">{renderFields()}</div>

							<div className="space-y-2 pt-4 border-t">
								<FormField
									control={form.control}
									name="reason"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-sm font-medium">
												Reason for Changes{' '}
												<span className="text-red-500">*</span>
											</FormLabel>
											<FormControl>
												<Textarea
													{...field}
													placeholder="Please provide a detailed reason for these changes..."
													className="min-h-[100px] w-full resize-none"
												/>
											</FormControl>
											<p className="text-xs text-muted-foreground">
												Provide a clear explanation to help expedite the
												approval process.
											</p>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</div>
					</form>
				</Form>

				<DialogFooter className="flex-shrink-0 pt-4 border-t">
					<Button type="button" variant="outline" onClick={onClose}>
						Cancel
					</Button>
					<Button
						type="button"
						onClick={form.handleSubmit(handleFormSubmit)}
						// className="bg-indigo-600 hover:bg-indigo-700"
					>
						Submit Request
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}

function AddChildDialog({ open, onSubmit, onClose }) {
	// Create a simple child validation schema
	const schema = z.object({
		name: z
			.string()
			.min(2, 'Child name must be at least 2 characters')
			.max(50, 'Child name must not exceed 50 characters')
			.regex(/^[a-zA-Z\s]+$/, 'Child name must contain only letters and spaces'),

		dob: z
			.string()
			.regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format')
			.refine((date) => {
				const birthDate = new Date(date);
				const today = new Date();
				const maxAge = new Date();
				maxAge.setFullYear(today.getFullYear() - 25);

				return birthDate >= maxAge && birthDate <= today;
			}, 'Child must be born within the last 25 years and not in the future'),

		nationality: z
			.enum(['singaporean', 'malaysian', 'indian', 'chinese', 'american', 'british', 'australian', 'other'], {
				errorMap: () => ({ message: 'Please select a valid nationality' }),
			}),

		reason: z
			.string()
			.min(10, 'Reason must be at least 10 characters')
			.max(500, 'Reason must not exceed 500 characters'),
	});

	// Initialize form with react-hook-form and Zod validation
	const form = useForm({
		resolver: zodResolver(schema),
		defaultValues: {
			name: '',
			dob: '',
			nationality: '',
			gender: '',
			birthCertificateNumber: '',
			reason: '',
		},
	});

	// Reset form when dialog opens/closes
	useEffect(() => {
		if (!open) {
			form.reset({
				name: '',
				dob: '',
				nationality: '',
				gender: '',
				birthCertificateNumber: '',
				reason: '',
			});
		}
	}, [open, form]);

	// Handle form submission
	const handleFormSubmit = (data) => {
		const { reason, ...childData } = data;
		onSubmit(childData, reason);
		form.reset();
	};

	if (!open) return null;

	return (
		<Dialog open={open} onOpenChange={onClose}>
			<DialogContent className="max-w-3xl w-[95vw] max-h-[90vh] overflow-hidden flex flex-col">
				<DialogHeader className="flex-shrink-0 pb-4 border-b">
					<DialogTitle className="text-xl font-semibold flex items-center gap-2">
						<Baby className="h-5 w-5 text-blue-500" />
						Add Child Information
					</DialogTitle>
					<DialogDescription className="text-sm text-muted-foreground">
						Request to add a new child to your family information. All additions require approval from your manager.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form onSubmit={form.handleSubmit(handleFormSubmit)} className="flex-1 overflow-y-auto py-4">
						<div className="space-y-6">
							<Alert className="border-amber-200 bg-amber-50">
								<AlertCircle className="h-4 w-4 text-amber-600" />
								<AlertTitle className="text-amber-800">
									Approval Required
								</AlertTitle>
								<AlertDescription className="text-amber-700">
									Child information will be reviewed by your reporting manager before being added to your profile.
								</AlertDescription>
							</Alert>

							<div className="space-y-6">
								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="name"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium">
													Child Name <span className="text-red-500">*</span>
												</FormLabel>
												<FormControl>
													<Input
														{...field}
														placeholder="Enter child's full name"
														className="w-full"
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="dob"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium">
													Date of Birth <span className="text-red-500">*</span>
												</FormLabel>
												<FormControl>
													<Input
														{...field}
														type="date"
														className="w-full"
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>

								<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
									<FormField
										control={form.control}
										name="gender"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium">
													Gender <span className="text-red-500">*</span>
												</FormLabel>
												<Select onValueChange={field.onChange} value={field.value}>
													<FormControl>
														<SelectTrigger className="w-full">
															<SelectValue placeholder="Select gender" />
														</SelectTrigger>
													</FormControl>
													<SelectContent className="z-50">
														<SelectItem value="male">Male</SelectItem>
														<SelectItem value="female">Female</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name="nationality"
										render={({ field }) => (
											<FormItem>
												<FormLabel className="text-sm font-medium">
													Nationality <span className="text-red-500">*</span>
												</FormLabel>
												<Select onValueChange={field.onChange} value={field.value}>
													<FormControl>
														<SelectTrigger className="w-full">
															<SelectValue placeholder="Select nationality" />
														</SelectTrigger>
													</FormControl>
													<SelectContent className="z-50">
														<SelectItem value="singaporean">Singaporean</SelectItem>
														<SelectItem value="malaysian">Malaysian</SelectItem>
														<SelectItem value="indian">Indian</SelectItem>
														<SelectItem value="chinese">Chinese</SelectItem>
														<SelectItem value="american">American</SelectItem>
														<SelectItem value="british">British</SelectItem>
														<SelectItem value="australian">Australian</SelectItem>
														<SelectItem value="other">Other</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							<div className="space-y-2 pt-4 border-t">
								<FormField
									control={form.control}
									name="reason"
									render={({ field }) => (
										<FormItem>
											<FormLabel className="text-sm font-medium">
												Reason for Adding Child <span className="text-red-500">*</span>
											</FormLabel>
											<FormControl>
												<Textarea
													{...field}
													placeholder="Please provide a reason for adding this child (e.g., new birth, adoption, etc.)..."
													className="min-h-[100px] w-full resize-none"
												/>
											</FormControl>
											<p className="text-xs text-muted-foreground">
												Provide a clear explanation to help expedite the approval process.
											</p>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>
						</div>
					</form>
				</Form>

				<DialogFooter className="flex-shrink-0 pt-4 border-t">
					<Button type="button" variant="outline" onClick={onClose}>
						Cancel
					</Button>
					<Button
						type="button"
						onClick={form.handleSubmit(handleFormSubmit)}
						// className="bg-indigo-600 hover:bg-indigo-700"
					>
						Submit Request
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}

// Helper component for managing multiple entries (education, experience, contact)
function MultipleEntriesManager({ section, entries, onEntriesChange, form }) {
	const [editingIndex, setEditingIndex] = useState(null);
	const [showAddForm, setShowAddForm] = useState(false);

	const getEmptyEntry = () => {
		switch (section) {
			case 'education':
				return {
					instituteName: '',
					qualification: '',
					startDate: '',
					endDate: '',
					grade: '',
				};
			case 'experience':
				return {
					companyName: '',
					designation: '',
					periodFrom: '',
					periodTo: '',
					location: '',
					reasonForLeaving: '',
				};
			case 'contact':
				return {
					name: '',
					relationship: '',
					countryDialCode: '+65',
					phone: '',
					email: '',
					type: '',
				};
			default:
				return {};
		}
	};

	const addEntry = () => {
		const newEntry = getEmptyEntry();
		const updatedEntries = [...entries, newEntry];
		onEntriesChange(updatedEntries);
		setEditingIndex(updatedEntries.length - 1);
		setShowAddForm(true);
	};

	const updateEntry = (index, updatedEntry) => {
		const updatedEntries = [...entries];
		updatedEntries[index] = updatedEntry;
		onEntriesChange(updatedEntries);
	};

	const removeEntry = (index) => {
		const updatedEntries = entries.filter((_, i) => i !== index);
		onEntriesChange(updatedEntries);
		setEditingIndex(null);
		setShowAddForm(false);
	};

	const renderEntryForm = (entry, index) => {
		switch (section) {
			case 'education':
				return (
					<div className="space-y-4 p-4 border rounded-md bg-slate-50">
						<div className="flex justify-between items-center">
							<h5 className="font-medium">
								{index === entries.length - 1 && showAddForm ? 'Add New Education' : 'Edit Education'}
							</h5>
							<div className="flex gap-2">
								<Button
									type="button"
									size="sm"
									onClick={() => {
										setEditingIndex(null);
										setShowAddForm(false);
									}}
								>
									Cancel
								</Button>
								{index !== entries.length - 1 && (
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() => removeEntry(index)}
									>
										Remove
									</Button>
								)}
							</div>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<Label className="text-sm font-medium">Institution Name</Label>
								<Input
									value={entry.instituteName}
									onChange={(e) => updateEntry(index, { ...entry, instituteName: e.target.value })}
									placeholder="Enter institution name"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Qualification</Label>
								<Select
									value={entry.qualification}
									onValueChange={(value) => updateEntry(index, { ...entry, qualification: value })}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select qualification" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="HIGH_SCHOOL">High School</SelectItem>
										<SelectItem value="DIPLOMA">Diploma</SelectItem>
										<SelectItem value="BACHELOR">Bachelor&apos;s Degree</SelectItem>
										<SelectItem value="POST_GRADUATE">Post Graduate</SelectItem>
										<SelectItem value="DOCTORATE">Doctorate</SelectItem>
									</SelectContent>
								</Select>
							</div>
							<div>
								<Label className="text-sm font-medium">Start Date</Label>
								<Input
									type="date"
									value={entry.startDate}
									onChange={(e) => updateEntry(index, { ...entry, startDate: e.target.value })}
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">End Date</Label>
								<Input
									type="date"
									value={entry.endDate}
									onChange={(e) => updateEntry(index, { ...entry, endDate: e.target.value })}
								/>
							</div>
							<div className="md:col-span-2">
								<Label className="text-sm font-medium">Grade (Optional)</Label>
								<Input
									value={entry.grade || ''}
									onChange={(e) => updateEntry(index, { ...entry, grade: e.target.value })}
									placeholder="Enter grade/GPA"
								/>
							</div>
						</div>
					</div>
				);
			case 'experience':
				return (
					<div className="space-y-4 p-4 border rounded-md bg-slate-50">
						<div className="flex justify-between items-center">
							<h5 className="font-medium">
								{index === entries.length - 1 && showAddForm ? 'Add New Experience' : 'Edit Experience'}
							</h5>
							<div className="flex gap-2">
								<Button
									type="button"
									size="sm"
									onClick={() => {
										setEditingIndex(null);
										setShowAddForm(false);
									}}
								>
									Cancel
								</Button>
								{index !== entries.length - 1 && (
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() => removeEntry(index)}
									>
										Remove
									</Button>
								)}
							</div>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<Label className="text-sm font-medium">Company Name</Label>
								<Input
									value={entry.companyName}
									onChange={(e) => updateEntry(index, { ...entry, companyName: e.target.value })}
									placeholder="Enter company name"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Job Title</Label>
								<Input
									value={entry.designation}
									onChange={(e) => updateEntry(index, { ...entry, designation: e.target.value })}
									placeholder="Enter job title"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Start Date</Label>
								<Input
									type="date"
									value={entry.periodFrom}
									onChange={(e) => updateEntry(index, { ...entry, periodFrom: e.target.value })}
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">End Date</Label>
								<Input
									type="date"
									value={entry.periodTo}
									onChange={(e) => updateEntry(index, { ...entry, periodTo: e.target.value })}
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Location</Label>
								<Input
									value={entry.location}
									onChange={(e) => updateEntry(index, { ...entry, location: e.target.value })}
									placeholder="Enter location"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Reason for Leaving (Optional)</Label>
								<Input
									value={entry.reasonForLeaving || ''}
									onChange={(e) => updateEntry(index, { ...entry, reasonForLeaving: e.target.value })}
									placeholder="Enter reason for leaving"
								/>
							</div>
						</div>
					</div>
				);
			case 'contact':
				return (
					<div className="space-y-4 p-4 border rounded-md bg-slate-50">
						<div className="flex justify-between items-center">
							<h5 className="font-medium">
								{index === entries.length - 1 && showAddForm ? 'Add New Contact' : 'Edit Contact'}
							</h5>
							<div className="flex gap-2">
								<Button
									type="button"
									size="sm"
									onClick={() => {
										setEditingIndex(null);
										setShowAddForm(false);
									}}
								>
									Cancel
								</Button>
								{index !== entries.length - 1 && (
									<Button
										type="button"
										size="sm"
										variant="destructive"
										onClick={() => removeEntry(index)}
									>
										Remove
									</Button>
								)}
							</div>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
							<div>
								<Label className="text-sm font-medium">Contact Name</Label>
								<Input
									value={entry.name}
									onChange={(e) => updateEntry(index, { ...entry, name: e.target.value })}
									placeholder="Enter contact name"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Relationship</Label>
								<Select
									value={entry.relationship}
									onValueChange={(value) => updateEntry(index, { ...entry, relationship: value })}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select relationship" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="Spouse">Spouse</SelectItem>
										<SelectItem value="Parent">Parent</SelectItem>
										<SelectItem value="Child">Child</SelectItem>
										<SelectItem value="Sibling">Sibling</SelectItem>
										<SelectItem value="Friend">Friend</SelectItem>
										<SelectItem value="Other">Other</SelectItem>
									</SelectContent>
								</Select>
							</div>
							<div>
								<Label className="text-sm font-medium">Country Code</Label>
								<Input
									value={entry.countryDialCode}
									onChange={(e) => updateEntry(index, { ...entry, countryDialCode: e.target.value })}
									placeholder="+65"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Phone Number</Label>
								<Input
									value={entry.phone}
									onChange={(e) => updateEntry(index, { ...entry, phone: e.target.value })}
									placeholder="Enter phone number"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Email (Optional)</Label>
								<Input
									type="email"
									value={entry.email || ''}
									onChange={(e) => updateEntry(index, { ...entry, email: e.target.value })}
									placeholder="Enter email address"
								/>
							</div>
							<div>
								<Label className="text-sm font-medium">Contact Type</Label>
								<Select
									value={entry.type}
									onValueChange={(value) => updateEntry(index, { ...entry, type: value })}
								>
									<SelectTrigger>
										<SelectValue placeholder="Select contact type" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="emergency">Emergency</SelectItem>
										<SelectItem value="reference">Reference</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</div>
					</div>
				);
			default:
				return null;
		}
	};

	const renderEntryDisplay = (entry, index) => {
		switch (section) {
			case 'education':
				return (
					<div key={index} className="p-3 border rounded-md bg-white">
						<div className="flex justify-between items-start">
							<div className="space-y-1">
								<h5 className="font-medium">{entry.instituteName}</h5>
								<p className="text-sm text-muted-foreground">
									{entry.qualification} • {formatDate(entry.startDate)} - {formatDate(entry.endDate)}
								</p>
								{entry.grade && (
									<p className="text-sm">Grade: {entry.grade}</p>
								)}
							</div>
							<Button
								type="button"
								size="sm"
								variant="outline"
								onClick={() => setEditingIndex(index)}
							>
								<Pencil className="h-4 w-4" />
							</Button>
						</div>
					</div>
				);
			case 'experience':
				return (
					<div key={index} className="p-3 border rounded-md bg-white">
						<div className="flex justify-between items-start">
							<div className="space-y-1">
								<h5 className="font-medium">{entry.companyName}</h5>
								<p className="text-sm font-medium">{entry.designation}</p>
								<p className="text-sm text-muted-foreground">
									{formatDate(entry.periodFrom)} - {formatDate(entry.periodTo)} • {entry.location}
								</p>
								{entry.reasonForLeaving && (
									<p className="text-xs text-muted-foreground">
										Reason: {entry.reasonForLeaving}
									</p>
								)}
							</div>
							<Button
								type="button"
								size="sm"
								variant="outline"
								onClick={() => setEditingIndex(index)}
							>
								<Pencil className="h-4 w-4" />
							</Button>
						</div>
					</div>
				);
			case 'contact':
				return (
					<div key={index} className="p-3 border rounded-md bg-white">
						<div className="flex justify-between items-start">
							<div className="space-y-1">
								<h5 className="font-medium">{entry.name}</h5>
								<p className="text-sm text-muted-foreground">
									{entry.relationship} • {capitalize(entry.type)}
								</p>
								<p className="text-sm">
									{entry.countryDialCode} {entry.phone}
								</p>
								{entry.email && (
									<p className="text-sm text-muted-foreground">{entry.email}</p>
								)}
							</div>
							<Button
								type="button"
								size="sm"
								variant="outline"
								onClick={() => setEditingIndex(index)}
							>
								<Pencil className="h-4 w-4" />
							</Button>
						</div>
					</div>
				);
			default:
				return null;
		}
	};

	return (
		<div className="space-y-4">
			<div className="flex justify-between items-center">
				<h4 className="text-sm font-medium capitalize">{section} Entries</h4>
				<Button
					type="button"
					size="sm"
					onClick={addEntry}
					className="flex items-center gap-2"
				>
					<Plus className="h-4 w-4" />
					Add {section}
				</Button>
			</div>

			{entries.length === 0 ? (
				<p className="text-sm text-muted-foreground italic">
					No {section} entries added yet
				</p>
			) : (
				<div className="space-y-3">
					{entries.map((entry, index) => (
						editingIndex === index ?
							renderEntryForm(entry, index) :
							renderEntryDisplay(entry, index)
					))}
				</div>
			)}
		</div>
	);
}