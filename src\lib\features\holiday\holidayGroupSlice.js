import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { customFetch, showErrors } from '@/lib/utils';
import { toast } from 'sonner';

const initialState = {
	holidayGroups: [],
  holidayGroupEmployees: [],
	isLoading: false,
};

export const fetchHolidayGroups = createAsyncThunk(
	'holidayGroup/fetchHolidayGroups',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch('/holiday-groups');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const addHolidayGroup = createAsyncThunk(
	'holidayGroup/addHolidayGroup',
	async (holidayGroupDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/holiday-groups',
				holidayGroupDetails
			);

			if (data?.success) {
				thunkAPI.dispatch(fetchHolidayGroups());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const copyHolidayGroup = createAsyncThunk(
	'holidayGroup/copyHolidayGroup',
	async (copyHolidayGroupDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/holiday-groups/copy',
				copyHolidayGroupDetails
			);

			if (data?.success) {
				thunkAPI.dispatch(fetchHolidayGroups());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateHolidayGroup = createAsyncThunk(
	'holidayGroup/updateHolidayGroup',
	async (holidayGroupDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/holiday-groups',
				holidayGroupDetails
			);

			if (data?.success) {
				thunkAPI.dispatch(fetchHolidayGroups());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const deleteHolidayGroup = createAsyncThunk(
	'holidayGroup/deleteHolidayGroup',
	async (holidayGroupIds, thunkAPI) => {
		try {
			const { data } = await customFetch.patch('/holiday-groups/remove', {
				holidayGroupIds,
			});

			if (data?.success) {
				thunkAPI.dispatch(fetchHolidayGroups());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const fetchEmployeeByHolidayGroupId = createAsyncThunk(
	'holidayGroup/fetchEmployeeByHolidayGroupId',
	async (holidayGroupId, thunkAPI) => {
		try {
			const { data } = await customFetch.get(
				`/holiday-groups/${holidayGroupId}/employees`
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const removeEmployeeFromHolidayGroup = createAsyncThunk(
	'holidayGroup/removeEmployeeFromHolidayGroup',
	async ({ holidayGroupId, employeeId }, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				`/holiday-groups/${holidayGroupId}/employees/${employeeId}`
			);

			if (data?.success) {
				thunkAPI.dispatch(fetchEmployeeByHolidayGroupId(holidayGroupId));
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const holidayGroupSlice = createSlice({
	name: 'holidayGroup',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchHolidayGroups.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchHolidayGroups.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.holidayGroups = payload.data.holidayGroups;
			})
			.addCase(fetchHolidayGroups.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(addHolidayGroup.pending, (state) => {
				state.isLoading = true;
				toast.info('Creating Holiday Group...');
			})
			.addCase(addHolidayGroup.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(addHolidayGroup.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(copyHolidayGroup.pending, (state) => {
				state.isLoading = true;
				toast.info('Copying Holiday Group...');
			})
			.addCase(copyHolidayGroup.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(copyHolidayGroup.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateHolidayGroup.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Holiday Group...');
			})
			.addCase(updateHolidayGroup.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateHolidayGroup.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(deleteHolidayGroup.pending, (state) => {
				state.isLoading = true;
				toast.info('Deleting Holiday Group...');
			})
			.addCase(deleteHolidayGroup.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(deleteHolidayGroup.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(fetchEmployeeByHolidayGroupId.pending, (state, { payload }) => {
				state.isLoading = true;
			})
			.addCase(
				fetchEmployeeByHolidayGroupId.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					state.holidayGroupEmployees = payload.data;
				}
			)
			.addCase(fetchEmployeeByHolidayGroupId.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(removeEmployeeFromHolidayGroup.pending, (state) => {
				state.isLoading = true;
				toast.info('Removing Employee from Holiday Group...');
			})
			.addCase(
				removeEmployeeFromHolidayGroup.fulfilled,
				(state, { payload }) => {
					state.isLoading = false;
					toast.success(payload.message);
				}
			)
			.addCase(
				removeEmployeeFromHolidayGroup.rejected,
				(state, { payload }) => {
					state.isLoading = false;
					showErrors(payload);
				}
			);
	},
});

// export const {} = holidayGroupSlice.actions;
export default holidayGroupSlice.reducer;
