'use client';

import { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Check, MapPin, Calendar, DollarSign } from 'lucide-react';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import Image from 'next/image';
import { changeCompany } from '@/lib/features/glorified-client-admin/glorifiedClientAdminSlice';
import { useAppSelector } from '@/lib/hooks';

export function CompanyTiles() {
	const dispatch = useDispatch();
	const { companies } = useSelector((store) => store.glorifiedClientAdmin);
	const { companyData } = useSelector((store) => store.clientAdmin);

	const [selectedCompany, setSelectedCompany] = useState(null);
	const [showConfirmation, setShowConfirmation] = useState(false);
	const [selectedCountry, setSelectedCountry] = useState(null);

	const handleCompanyClick = (company) => {
		setSelectedCompany(company);
		setShowConfirmation(true);
	};

	const handleConfirmChange = () => {
		if (selectedCompany) {
			dispatch(changeCompany(selectedCompany._id));
		}
		setShowConfirmation(false);
	};

	// If no companies exist
	if (companies.length === 0) {
		return (
			<div className="text-center p-8">
				<h3 className="text-lg font-medium">No companies available</h3>
				<p className="text-muted-foreground">Add companies to get started.</p>
			</div>
		);
	}

	return (
		<div className="space-y-8">
			<div className="space-y-4">
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					{companies.map((company) => {
						// Check if this company is the currently selected one from companyData
						const isCurrentCompany =
							company._id === companyData?.companyDetails?._id;

						return (
							<Card
								key={company._id}
								className={cn(
									'cursor-pointer transition-all hover:shadow-md',
									isCurrentCompany && 'border-primary bg-primary/5'
								)}
								onClick={() => !isCurrentCompany && handleCompanyClick(company)}
							>
								<CardHeader className="pb-2">
									<div className="flex justify-between items-start">
										<div className="flex items-center gap-3">
											{company.logo && (
												<div className="h-10 w-10 rounded-md overflow-hidden bg-gray-100 flex items-center justify-center">
													<Image
														width={40}
														height={40}
														src={company.logo || '/placeholder.svg'}
														alt={`${company.businessName} logo`}
														className="h-full w-full object-contain"
													/>
												</div>
											)}
											<CardTitle className="text-lg">
												{company.businessName}
											</CardTitle>
										</div>
										{isCurrentCompany && (
											<Badge className="bg-primary">
												<Check className="h-3 w-3 mr-1" /> Current
											</Badge>
										)}
									</div>
									<CardDescription>
										Registration: {company.registration}
									</CardDescription>
								</CardHeader>
								<CardContent className="space-y-2">
									<div className="flex items-center text-sm">
										<MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
										<span className="truncate">{company.address}</span>
									</div>
									<div className="flex items-center text-sm">
										<DollarSign className="h-4 w-4 mr-2 text-muted-foreground" />
										<span>Currency: {company.currency}</span>
									</div>
									<div className="flex items-center text-sm">
										<Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
										<span>Date Format: {company.dateFormat}</span>
									</div>
								</CardContent>
								<CardFooter>
									{!isCurrentCompany && (
										<Button variant="outline" size="sm" className="w-full">
											Switch to this company
										</Button>
									)}
								</CardFooter>
							</Card>
						);
					})}
				</div>
			</div>
			{/* {Object.entries(countryGroups).map(([country, countryData]) => {
				if (!countryData) return null;

				return (
					<div key={country} className="space-y-4">


						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
							{companies.map((company) => {
								// Check if this company is the currently selected one from companyData
								const isCurrentCompany =
									company._id === companyData?.companyDetails?._id;

								return (
									<Card
										key={company._id}
										className={cn(
											'cursor-pointer transition-all hover:shadow-md',
											isCurrentCompany && 'border-primary bg-primary/5'
										)}
										onClick={() =>
											!isCurrentCompany && handleCompanyClick(company)
										}
									>
										<CardHeader className="pb-2">
											<div className="flex justify-between items-start">
												<div className="flex items-center gap-3">
													{company.logo && (
														<div className="h-10 w-10 rounded-md overflow-hidden bg-gray-100 flex items-center justify-center">
															<Image
																width={40}
																height={40}
																src={company.logo || '/placeholder.svg'}
																alt={`${company.businessName} logo`}
																className="h-full w-full object-contain"
															/>
														</div>
													)}
													<CardTitle className="text-lg">
														{company.businessName}
													</CardTitle>
												</div>
												{isCurrentCompany && (
													<Badge className="bg-primary">
														<Check className="h-3 w-3 mr-1" /> Current
													</Badge>
												)}
											</div>
											<CardDescription>
												Registration: {company.registration}
											</CardDescription>
										</CardHeader>
										<CardContent className="space-y-2">
											<div className="flex items-center text-sm">
												<MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
												<span className="truncate">{company.address}</span>
											</div>
											<div className="flex items-center text-sm">
												<DollarSign className="h-4 w-4 mr-2 text-muted-foreground" />
												<span>Currency: {company.currency}</span>
											</div>
											<div className="flex items-center text-sm">
												<Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
												<span>Date Format: {company.dateFormat}</span>
											</div>
										</CardContent>
										<CardFooter>
											{!isCurrentCompany && (
												<Button variant="ghost" size="sm" className="w-full">
													Switch to this company
												</Button>
											)}
										</CardFooter>
									</Card>
								);
							})}
						</div>
					</div>
				);
			})} */}

			<ConfirmationDialog
				open={showConfirmation}
				onOpenChange={setShowConfirmation}
				title="Switch Company"
				description={`Are you sure you want to switch to ${selectedCompany?.businessName}?`}
				onConfirm={handleConfirmChange}
			/>
		</div>
	);
}

export function ConfirmationDialog({
	open,
	onOpenChange,
	title,
	description,
	onConfirm,
	confirmText = 'Confirm',
	cancelText = 'Cancel',
}) {
	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>{title}</DialogTitle>
					<DialogDescription>{description}</DialogDescription>
				</DialogHeader>
				<DialogFooter>
					<Button variant="outline" onClick={() => onOpenChange(false)}>
						{cancelText}
					</Button>
					<Button onClick={onConfirm}>{confirmText}</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
