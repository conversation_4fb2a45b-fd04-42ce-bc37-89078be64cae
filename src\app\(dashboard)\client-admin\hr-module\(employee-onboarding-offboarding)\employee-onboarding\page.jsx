'use client';
import { EmployeeContactDetailsForm } from '@/components/employee-contact-form';
import { EmployeeEducationSkillsForm } from '@/components/employee-qualifications-form';
import { EmployeePersonalDetailsForm } from '@/components/employee-personal-details-form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { Progress } from '@/components/ui/progress';
import { useState, useEffect } from 'react';
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { EmployeeEmploymentDetailsForm } from '@/components/employee-employment-form';
import { EmployeeEarningsForm } from '@/components/employee-earning-form';
import { EmployeeBenefitsForm } from '@/components/employee-details-benefits-form';
import { useSearchParams } from 'next/navigation';
import { fetchEmployeeDetails } from '@/lib/features/employees/employeeSlice';

export default function EmployeeRegistrationPage() {
	const dispatch = useAppDispatch();
	const { step, employeeDetails } = useAppSelector(
		(store) => store.employee
	);
	const { companyData } = useAppSelector((store) => store.companyDetails);

	const employeeId = useSearchParams().get('employeeId');

	useEffect(() => {
		if (employeeId) {
			dispatch(fetchEmployeeDetails(employeeId));
		}
	}, [employeeId, dispatch]);

	const [showDialog, setShowDialog] = useState(false);
	const [isChecked, setIsChecked] = useState(false);

	useEffect(() => {
		if (
			companyData &&
			companyData.ownerUserFlags.isClientRegistrationAsEmployeeComplete ===
				false
		) {
			setShowDialog(true);
		}
	}, [companyData]);

	const handleCheckboxChange = () => {
		setIsChecked(!isChecked);
	};

	const handleDialogClose = () => {
		setShowDialog(false);
	};

	const stepComponents = [
		{
			id: 1,
			component: <EmployeePersonalDetailsForm />,
			title: 'Personal Details',
		},
		{
			id: 2,
			component: <EmployeeEducationSkillsForm />,
			title: 'Qualifications',
		},
		{
			id: 3,
			component: <EmployeeContactDetailsForm />,
			title: 'Contact Details',
		},
		{
			id: 4,
			component: <EmployeeEmploymentDetailsForm />,
			title: 'Employment Details',
		},
		{
			id: 5,
			component: <EmployeeEarningsForm />,
			title: 'Earning Details',
		},
		{
			id: 6,
			component: <EmployeeBenefitsForm />,
			title: 'Benefits Details',
		},
	];
	return (
		<section className="w-full h-full p-4">
			<AlertDialog open={showDialog} onOpenChange={setShowDialog}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle>Important Notice</AlertDialogTitle>
						<AlertDialogDescription>
							As an Admin, you are the first employee of this organization. You
							need to onboard yourself using this form before onboarding other
							employees.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<div className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4 shadow">
						<Checkbox
							id="instructions"
							checked={isChecked}
							onCheckedChange={handleCheckboxChange}
						/>
						<Label
							htmlFor="instructions"
							className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
						>
							I have read the instructions and will proceed now
						</Label>
					</div>
					<AlertDialogFooter>
						<AlertDialogAction
							onClick={handleDialogClose}
							disabled={!isChecked}
						>
							Proceed
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
			<header className="grid grid-cols-12 gap-2">
				<div className="flex flex-col gap-2 col-span-9">
					<h1 className="text-xl md:text-2xl font-semibold">
						Employee Onboarding
					</h1>
					<p className="font-medium text-gray-500">
						Please fill in the required information to onboard a new employee.
					</p>
				</div>
				{employeeDetails && (
					<div className="flex flex-col gap-2 col-span-3 items-end">
						<h1 className="text-lg md:text-xl font-semibold">
							{employeeDetails.name ||
								employeeDetails.personalDetails?.nameOnNRIC}
						</h1>
						<p className="font-medium text-gray-500">
							{employeeDetails.employeeOrgId ||
								employeeDetails.personalDetails?.employeeOrgId}
						</p>
					</div>
				)}

				<Separator />
			</header>
			<main className="w-full mt-4">
				<Progress value={(step / 5) * 100} className="mb-4" />
				<Card>
					<CardHeader>
						<CardTitle className="text-xl">
							{stepComponents[step - 1]?.title}
						</CardTitle>
						<Separator />
					</CardHeader>
					<CardContent>{stepComponents[step - 1]?.component} </CardContent>
				</Card>
			</main>
		</section>
	);
}

{
	/*

<Tabs defaultValue="personal-details" className="w-full">
					<TabsList className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 w-full mb-4">
						<TabsTrigger
							className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
							value="personal-details"
						>
							Personal
						</TabsTrigger>
						<TabsTrigger
							className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
							value="qualifications"
						>
							Qualifications
						</TabsTrigger>
						{/* <TabsTrigger
							className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
							value="experience"
						>
							Experience
						</TabsTrigger> */
}
// <TabsTrigger
// 	className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
// 	value="emergency-reference"
// >
// 	Contact
// </TabsTrigger>
{
	/* <TabsTrigger
							className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
							value="family-details"
						>
							Family Details
						</TabsTrigger> */
}
// 	<TabsTrigger
// 		className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
// 		value="employment"
// 	>
// 		Employment
// 	</TabsTrigger>
// 	<TabsTrigger
// 		className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
// 		value="earnings"
// 	>
// 		Earnings
// 	</TabsTrigger>
// 	<TabsTrigger
// 		className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
// 		value="benefits"
// 	>
// 		Benefits
// 	</TabsTrigger>
// 	{authenticatedUser.role === 'clientAdmin' && (
// 		<TabsTrigger
// 			className="flex-shrink-0 text-sm md:text-md whitespace-nowrap px-2 md:px-4"
// 			value="Discontinuation"
// 		>
// 			Discontinuation
// 		</TabsTrigger>
// 	)}
// </TabsList>
// <TabsContent value="personal-details">
// 	<Card>
// 		<CardHeader>
// 			<CardTitle className="text-xl">Personal Details</CardTitle>
// 			<Separator />
// 		</CardHeader>
// 		<CardContent>
// 			<EmployeePersonalDetailsForm />
// 		</CardContent>
// 	</Card>
// </TabsContent>
// <TabsContent value="qualifications">
// 	<Card>
// 		<CardHeader>
// 			<CardTitle className="text-xl">Qualifications</CardTitle>
// 			<Separator />
// 		</CardHeader>
// 		<CardContent>
// 			<EmployeeEducationSkillsForm />
// 		</CardContent>
// 	</Card>
// </TabsContent>
{
	/* <TabsContent value="experience">
						<Card>
							<CardHeader>
								<CardTitle className="text-xl">Experience</CardTitle>
								<Separator />
							</CardHeader>
							<CardContent>
								<EmployeeExperienceDetailsForm />
							</CardContent>
						</Card>
					</TabsContent> */
}
// <TabsContent value="emergency-reference">
// 	<Card>
// 		<CardHeader>
// 			<CardTitle className="text-xl">Contacts</CardTitle>
// 			<Separator />
// 		</CardHeader>
// 		<CardContent>
// 			<EmployeeContactDetailsForm />
// 		</CardContent>
// 	</Card>
// </TabsContent>
{
	/* <TabsContent value="family-details">
						<h2 className="text-lg font-semibold">Family Details</h2>
						<p>
							Please fill in the required information to register a new
							employee.
						</p>
					</TabsContent> */
}
// 	<TabsContent value="employment">
// 		<h2 className="text-lg font-semibold">Employment</h2>
// 		<p>Work in progress...</p>
// 	</TabsContent>
// 	<TabsContent value="earnings">
// 		<h2 className="text-lg font-semibold">Earnings</h2>
// 		<p>Work in progress...</p>
// 	</TabsContent>
// 	<TabsContent value="benefits">
// 		<h2 className="text-lg font-semibold">Benefits</h2>
// 		<p>Work in progress...</p>
// 	</TabsContent>
// 	{authenticatedUser.role === 'clientAdmin' && (
// 		<TabsContent value="discontinuation">
// 			<h2 className="text-lg font-semibold">Discontinuation</h2>
// 			<p>Work in progress...</p>
// 		</TabsContent>
// 	)}
// </Tabs>
