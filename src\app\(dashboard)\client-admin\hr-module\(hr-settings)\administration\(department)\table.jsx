'use client';

import { useState, useCallback, useMemo, useEffect } from 'react';
import {
	getCoreRowModel,
	getFilteredRowModel,
	getPaginationRowModel,
	getSortedRowModel,
	useReactTable,
} from '@tanstack/react-table';
import { TableActions } from '@/components/table-actions';
import { TablePagination } from '@/components/table-pagination';
import { createColumns } from './columns';
import debounce from 'lodash.debounce';
import { TBody } from '@/components/table-body';
import { THeader } from '@/components/table-header';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { Table } from '@/components/ui/table';
import {
	deleteDepartment,
	fetchDepartments,
} from '@/lib/features/company-infrastructure/departmentSlice';

export default function DepartmentTable() {
	const [sorting, setSorting] = useState([]);
	const [columnFilters, setColumnFilters] = useState([]);
	const [columnVisibility, setColumnVisibility] = useState({});
	const [rowSelection, setRowSelection] = useState({});
	const [globalFilter, setGlobalFilter] = useState('');
	const [isEditing, setIsEditing] = useState(false);
	const dispatch = useAppDispatch();
	const { departments: data, isLoading } = useAppSelector(
		(store) => store.department
	);
	const columns = useMemo(
		() => createColumns(dispatch, isEditing, setIsEditing),
		[dispatch, isEditing, setIsEditing]
	);

  console.log('Department data:', data);
  console.log('Department columns:', columns);
	useEffect(() => {
		dispatch(fetchDepartments({ limit: 'all' }));
	}, [dispatch]);

	const table = useReactTable({
		data,
		columns,
		onSortingChange: setSorting,
		onColumnFiltersChange: setColumnFilters,
		getCoreRowModel: getCoreRowModel(),
		getPaginationRowModel: getPaginationRowModel(),
		getSortedRowModel: getSortedRowModel(),
		getFilteredRowModel: getFilteredRowModel(),
		onColumnVisibilityChange: setColumnVisibility,
		onRowSelectionChange: setRowSelection,
		onGlobalFilterChange: setGlobalFilter,
		state: {
			sorting,
			columnFilters,
			columnVisibility,
			rowSelection,
			globalFilter,
		},
	});

	const debouncedSetGlobalFilter = useMemo(
		() =>
			debounce((value) => {
				setGlobalFilter(value);
			}, 300),
		[]
	);

	const handleFilterChange = useCallback(
		(event) => {
			debouncedSetGlobalFilter(event.target.value);
		},
		[debouncedSetGlobalFilter]
	);

	const handleBulkAction = async (action) => {
		const selectedRows = table.getFilteredSelectedRowModel().rows;

		switch (action) {
			case 'delete':
				const selectedIds = selectedRows.map((row) => row.original._id);

				await dispatch(deleteDepartment(selectedIds));
				break;

			default:
				break;
		}
	};

	return (
		<div className="overflow-x-auto">
			<TableActions
				table={table}
				handleFilterChange={handleFilterChange}
				handleBulkAction={handleBulkAction}
				bulkActions={[{ label: 'Delete', value: 'delete' }]}
			/>
			<div className="rounded-md border max-w-[95vw] lg:max-w-full">
				<Table>
					<THeader table={table} />
					<TBody table={table} isLoading={isLoading} />
				</Table>
			</div>
			<TablePagination table={table} />
		</div>
	);
}
