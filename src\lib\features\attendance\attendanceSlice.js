import { customFetch, showErrors } from '@/lib/utils';
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { toast } from 'sonner';

const initialState = {
	currentLogStatus: null,
	clockInDelay: null,
	clockOutDelay: null,
	clockInLimit: null,
	clockOutLimit: null,
	attendanceLogs: [],
	isLoading: false,
};

export const getAttendanceLogs = createAsyncThunk(
	'/attendance/getAttendanceLogs',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/attendance/logs');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const getStatus = createAsyncThunk(
	'/attendance/getStatus',
	async (_, thunkAPI) => {
		try {
			const { data } = await customFetch.get('/attendance/status');
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const clockIn = createAsyncThunk(
	'/attendance/clockIn',
	async (clockInDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/attendance/clock-in',
				clockInDetails
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const startBreak = createAsyncThunk(
	'/attendance/startBreak',
	async (startBreakDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/attendance/start-break',
				startBreakDetails
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const endBreak = createAsyncThunk(
	'/attendance/endBreak',
	async (endBreakDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/attendance/end-break',
				endBreakDetails
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const clockOut = createAsyncThunk(
	'/attendance/clockOut',
	async (clockOutDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/attendance/clock-out',
				clockOutDetails
			);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const attendanceSlice = createSlice({
	name: 'attendance',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(getAttendanceLogs.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(getAttendanceLogs.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.attendanceLogs = payload?.data[0]?.attendanceTime;
				state.clockInDelay = payload?.data[0]?.clockInDelay;
				state.clockOutDelay = payload?.data[0]?.clockOutDelay;
				state.clockInLimit = payload?.data[0]?.clockInLimit;
				state.clockOutLimit = payload?.data[0]?.clockOutLimit;
			})
			.addCase(getAttendanceLogs.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload, toast);
			})
			.addCase(getStatus.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(getStatus.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.currentLogStatus = payload.data;
			})
			.addCase(getStatus.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload, toast);
			})
			.addCase(clockIn.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(clockIn.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.currentLogStatus = payload.data;
				toast.success(payload.message);
			})
			.addCase(clockIn.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload, toast);
			})
			.addCase(startBreak.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(startBreak.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.currentLogStatus = payload.data;
				toast.success(payload.message);
			})
			.addCase(startBreak.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload, toast);
			})
			.addCase(endBreak.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(endBreak.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.currentLogStatus = payload.data;
				toast.success(payload.message);
			})
			.addCase(endBreak.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload, toast);
			})
			.addCase(clockOut.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(clockOut.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.currentLogStatus = payload.data;
				toast.success(payload.message);
			})
			.addCase(clockOut.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload, toast);
			});
	},
});

export const {} = attendanceSlice.actions;
export default attendanceSlice.reducer;
