'use client';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
	ArrowUpDown,
	User,
	FileText,
	Clock,
	CheckCircle,
	XCircle,
	Eye,
	Check,
	X,
} from 'lucide-react';
import { format } from 'date-fns';
import { EditRequestRowActions } from './row-actions';

// Helper function to format section names
const formatSectionName = (section) => {
	return section
		.split('-')
		.map(word => word.charAt(0).toUpperCase() + word.slice(1))
		.join(' ');
};

// Helper function to get status badge variant
const getStatusBadgeVariant = (status) => {
	switch (status) {
		case 'pending':
			return 'secondary';
		case 'approved':
			return 'default';
		case 'rejected':
			return 'destructive';
		default:
			return 'secondary';
	}
};

// Helper function to get status icon
const getStatusIcon = (status) => {
	switch (status) {
		case 'pending':
			return <Clock className="h-3 w-3" />;
		case 'approved':
			return <CheckCircle className="h-3 w-3" />;
		case 'rejected':
			return <XCircle className="h-3 w-3" />;
		default:
			return <Clock className="h-3 w-3" />;
	}
};

export const createColumns = ({ onCompareData, onApproveRequest, onRejectRequest }) => {
	return [
		{
			accessorKey: 'userId.employeeOrgId',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Employee ID
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="font-medium">
					{row.original.userId.employeeOrgId}
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'userId.nameOnNRIC',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Employee Name
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<User className="h-4 w-4 text-muted-foreground" />
					<div>
						<div className="font-medium">{row.original.userId.nameOnNRIC}</div>
						<div className="text-sm text-muted-foreground">
							{row.original.userId.email}
						</div>
					</div>
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'section',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Section
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="flex items-center gap-2">
					<FileText className="h-4 w-4 text-muted-foreground" />
					<span className="font-medium">
						{formatSectionName(row.original.section)}
					</span>
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'status',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Status
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => {
				const status = row.original.status;
				return (
					<Badge variant={getStatusBadgeVariant(status)} className="gap-1">
						{getStatusIcon(status)}
						{status.charAt(0).toUpperCase() + status.slice(1)}
					</Badge>
				);
			},
			enableSorting: true,
		},
		{
			accessorKey: 'submittedAt',
			header: ({ column }) => (
				<Button
					variant="ghost"
					onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
					className="p-0 hover:bg-transparent"
				>
					Submitted At
					<ArrowUpDown className="ml-2 h-4 w-4" />
				</Button>
			),
			cell: ({ row }) => (
				<div className="text-sm">
					{format(new Date(row.original.submittedAt), 'MMM dd, yyyy HH:mm')}
				</div>
			),
			enableSorting: true,
		},
		{
			accessorKey: 'submittedBy.nameOnNRIC',
			header: 'Submitted By',
			cell: ({ row }) => (
				<div className="text-sm">
					{row.original.submittedBy.nameOnNRIC}
				</div>
			),
			enableSorting: false,
		},
		{
			id: 'actions',
			header: 'Actions',
			cell: ({ row }) => (
				<EditRequestRowActions
					request={row.original}
					onCompareData={onCompareData}
					onApproveRequest={onApproveRequest}
					onRejectRequest={onRejectRequest}
				/>
			),
			enableSorting: false,
		},
	];
};
