'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { useFieldArray, useForm, useWatch } from 'react-hook-form';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { useEffect, useState, useCallback } from 'react';
import { employmentDetailsSchema } from '@/lib/schemas/employeeRegistrationSchema';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	fetchBusinessUnitDepartments,
	fetchBusinessUnits,
} from '@/lib/features/company-infrastructure/businessUnitSlice';
import { userRoles } from '@/lib/utils';

import { Card, CardContent } from '@/components/ui/card';
import { PlusCircle, Trash2, Loader2 } from 'lucide-react';
import { Switch } from '@/components/ui/switch';
import { updateEmployeeEmploymentDetails } from '@/lib/features/employees/employeeSlice';
import { LoadingSubmitButton } from './loading-component';
import { fetchDepartmentDesignations } from '@/lib/features/company-infrastructure/departmentSlice';

const employeeRoles = [
	{ role: 'Business Unit Head', value: '5' },
	{ role: 'Department Head', value: '6' },
	{ role: 'Employee', value: '10' },
];

const workingDaysOptions = [
	{ value: '5_DAYS', label: '5 Days' },
	{ value: '5.5_DAYS', label: '5.5 Days' },
	{ value: '6_DAYS', label: '6 Days' },
];

const workingHoursOptions = [
	{ value: '4', label: '4 Hours' },
	{ value: '6', label: '6 Hours' },
	{ value: '8', label: '8 Hours' },
	{ value: '10', label: '10 Hours' },
];

const weekDayOptions = [
	{ value: 'MONDAY', label: 'Monday' },
	{ value: 'TUESDAY', label: 'Tuesday' },
	{ value: 'WEDNESDAY', label: 'Wednesday' },
	{ value: 'THURSDAY', label: 'Thursday' },
	{ value: 'FRIDAY', label: 'Friday' },
	{ value: 'SATURDAY', label: 'Saturday' },
	{ value: 'SUNDAY', label: 'Sunday' },
];

export function EmployeeEmploymentDetailsForm() {
	const dispatch = useAppDispatch();
	const { companyData } =
		useAppSelector((store) => store.companyDetails);
	const { businessUnits } =
		useAppSelector((store) => store.businessUnit);
	const { departments } =
		useAppSelector((store) => store.department);
	const { designations } =
		useAppSelector((store) => store.designation);
	const { authenticatedUser } = useAppSelector((store) => store.auth);
	const { employeeDetails, isLoading } = useAppSelector(
		(store) => store.employee
	);

	const [businessUnitHead, setBusinessUnitHead] = useState(null);
	const [departmentHead, setDepartmentHead] = useState(null);

	const form = useForm({
		resolver: zodResolver(employmentDetailsSchema),
		defaultValues: {
			source: 'job-advertisement',
			businessUnit: '',
			department: '',
			designation: '',
			reportingTo: '',
			employeeRole: '',
			probationPeriod: '3',
			employmentType: 'full-time',
			isBlocked: false,
			overTimeEligible: false,
			workSchedule: 'generic',
			workingDays: undefined,
			workingHours: undefined,
			firstOffDay: undefined,
			secondOffDay: undefined,
			halfDay: undefined,
			// Equipment defaults
			equipment: [],
		},
	});

	const {
		fields: equipmentFields,
		append: appendEquipment,
		remove: removeEquipment,
	} = useFieldArray({
		name: 'equipment',
		control: form.control,
	});

	const workSchedule = useWatch({
		control: form.control,
		name: 'workSchedule',
	});

	const workingDays = useWatch({
		control: form.control,
		name: 'workingDays',
	});

	const employmentType = useWatch({
		control: form.control,
		name: 'employmentType',
	});

	const selectedBusinessUnit = useWatch({
		control: form.control,
		name: 'businessUnit',
	});

	const selectedDepartment = useWatch({
		control: form.control,
		name: 'department',
	});

	useEffect(() => {
		dispatch(fetchBusinessUnits());
	}, [dispatch]);

	useEffect(() => {
		if (selectedBusinessUnit) {
			const businessUnitAdmin = businessUnits.find(
				(unit) => unit._id === selectedBusinessUnit
			).admin;
			setBusinessUnitHead(businessUnitAdmin);
			dispatch(fetchBusinessUnitDepartments(selectedBusinessUnit));
		}
	}, [selectedBusinessUnit, dispatch, businessUnits]);

	useEffect(() => {
		if (selectedDepartment) {
			dispatch(fetchDepartmentDesignations(selectedDepartment));
		}
	}, [selectedDepartment, dispatch]);

	// Clear designation when department changes
	useEffect(() => {
		if (selectedDepartment) {
			form.setValue('designation', '');
		}
	}, [selectedDepartment, form]);

	// Clear department when business unit changes
	useEffect(() => {
		if (selectedBusinessUnit) {
			form.setValue('department', '');
			form.setValue('designation', '');
		}
	}, [selectedBusinessUnit, form]);

	function onSubmit(data) {
		console.log(data);
		if (employeeDetails.employeeId || employeeDetails._id) {
			dispatch(
				updateEmployeeEmploymentDetails({
					...data,
					employeeId: employeeDetails.employeeId || employeeDetails._id,
				})
			);
		} else {
			toast.error('Employee ID not found, try editing the employee details.');
			return;
		}
	}

	const renderReportingManagers = useCallback(
		(employeeRole) => {
			const adminOption = (
				<SelectItem value={authenticatedUser.clientAdminId}>Admin</SelectItem>
			);

			const buHeadOption = businessUnitHead && (
				<SelectItem value={businessUnitHead.userId}>
					{businessUnitHead.nameOnNRIC}
				</SelectItem>
			);

			const deptHeadOption = departmentHead && (
				<SelectItem value={departmentHead.userId}>
					{departmentHead.nameOnNRIC}
				</SelectItem>
			);

			switch (employeeRole) {
				case '5': // BU Head → Always Admin
					console.log('in case 5');
					return adminOption;

				case '6': // Dept Head → Admin + BU Head if available
					console.log('in case 6');
					return (
						<>
							{adminOption}
							{buHeadOption}
						</>
					);

				case '10': // Employee → Admin + BU Head if available + Dept Head if available
					console.log('in case 7');
					return (
						<>
							{adminOption}
							{buHeadOption}
							{deptHeadOption}
						</>
					);

				default:
					return adminOption;
			}
		},
		[authenticatedUser, businessUnitHead, departmentHead]
	);

	/*

	use this in the event of debugging the form
	useEffect(() => {
		console.log(form.formState.errors);
	}, [form.formState.errors]);

	useEffect(() => {
		const subscription = form.watch((data) => {
			console.log(data);
		});
		return () => subscription.unsubscribe();
	}, [form]);

	*/
	useEffect(() => {
		console.log(form.formState.errors);
	}, [form.formState.errors]);

	useEffect(() => {
		const subscription = form.watch((data) => {
			console.log(data);
		});
		return () => subscription.unsubscribe();
	}, [form]);
	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
				<div className="space-y-4">
					<h3 className="text-lg font-medium">Employment Source</h3>
					<Separator />
					<FormField
						control={form.control}
						name="source"
						render={({ field }) => (
							<FormItem className="w-full md:w-1/2 lg:w-1/3">
								<FormLabel>Source</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
								>
									<FormControl>
										<SelectTrigger>
											<SelectValue placeholder="Select source" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										<SelectItem value="staff-recommendation">
											Staff Recommendation
										</SelectItem>
										<SelectItem value="job-advertisement">
											Job Advertisement
										</SelectItem>
									</SelectContent>
								</Select>
								<FormDescription>
									How the employee was sourced for this position
								</FormDescription>
								<FormMessage />
							</FormItem>
						)}
					/>
				</div>

				<div className="space-y-4">
					<h3 className="text-lg font-medium">Department Information</h3>
					<Separator />
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						<FormField
							control={form.control}
							name="businessUnit"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Business Unit</FormLabel>
									<Select onValueChange={field.onChange} value={field.value}>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select business unit" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{businessUnits?.map((unit) => (
												<SelectItem key={unit._id} value={unit._id}>
													{unit.name} ({unit.location})
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<FormDescription>
										Business Units can be created from HR Settings
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="department"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Department</FormLabel>
									<Select
										onValueChange={field.onChange}
										value={field.value}
										disabled={!selectedBusinessUnit || departments.length === 0}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue
													placeholder={
														selectedBusinessUnit
															? 'Select department'
															: 'Select business unit first'
													}
												/>
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{departments.map((dept) => (
												<SelectItem key={dept._id} value={dept._id}>
													{dept.name}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<FormDescription>
										Departments can be created from HR Settings
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>
						<FormField
							control={form.control}
							name="designation"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Designation</FormLabel>
									<Select
										onValueChange={field.onChange}
										value={field.value}
										disabled={!selectedDepartment || designations.length === 0}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue
													placeholder={
														selectedDepartment
															? 'Select designation'
															: 'Select department first'
													}
												/>
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{designations?.map((designation) => (
												<SelectItem
													key={designation._id}
													value={designation._id}
												>
													{designation.name}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
									<FormDescription>
										Designations can be created from HR Settings
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>
				</div>

				<div className="space-y-4">
					<h3 className="text-lg font-medium">Role Information</h3>
					<Separator />
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<FormField
							control={form.control}
							name="employeeRole"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Employee Role</FormLabel>
									<Select onValueChange={field.onChange} value={field.value}>
										<FormControl>
											<SelectTrigger>
												<SelectValue placeholder="Select employee role" />
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{(userRoles.CLIENT_ADMIN === authenticatedUser.role ||
												userRoles.GLORIFIED_CLIENT_ADMIN ===
													authenticatedUser.role) &&
											companyData?.ownerUserFlags
												?.isClientRegistrationAsEmployeeComplete === false ? (
												<SelectItem value={authenticatedUser.role.toString()}>
													Admin
												</SelectItem>
											) : (
												employeeRoles.map((role) => (
													<SelectItem key={role.value} value={role.value}>
														{role.role}
													</SelectItem>
												))
											)}
										</SelectContent>
									</Select>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="reportingTo"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Reporting Manager</FormLabel>
									<Select
										onValueChange={field.onChange}
										value={field.value}
										disabled={
											!selectedDepartment || !form.watch('employeeRole')
										}
									>
										<FormControl>
											<SelectTrigger>
												<SelectValue
													placeholder={
														selectedDepartment
															? 'Select employee role'
															: 'Select department first'
													}
												/>
											</SelectTrigger>
										</FormControl>
										<SelectContent>
											{renderReportingManagers(form.watch('employeeRole'))}
										</SelectContent>
									</Select>
									<FormDescription>
										Manager the employee will report to
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>
				</div>

				<div className="space-y-4">
					<h3 className="text-lg font-medium">Employment Terms</h3>
					<Separator />
					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<FormField
							control={form.control}
							name="probationPeriod"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Probation Period (months)</FormLabel>
									<FormControl>
										<Input
											type="number"
											min="0"
											max="12"
											placeholder="Enter probation period"
											{...field}
										/>
									</FormControl>
									<FormDescription>
										Number of months for probation (0-12)
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="employmentType"
							render={({ field }) => (
								<FormItem className="space-y-3">
									<FormLabel>Employment Type</FormLabel>
									<FormControl>
										<RadioGroup
											onValueChange={field.onChange}
											defaultValue={field.value}
											className="flex flex-col space-y-1"
										>
											<FormItem className="flex items-center space-x-3 space-y-0">
												<FormControl>
													<RadioGroupItem value="full-time" />
												</FormControl>
												<FormLabel className="font-normal">Full Time</FormLabel>
												<FormItem className="flex items-center space-x-3 space-y-0">
													<FormControl>
														<RadioGroupItem value="part-time" />
													</FormControl>
													<FormLabel className="font-normal">
														Part Time
													</FormLabel>
												</FormItem>
											</FormItem>
										</RadioGroup>
									</FormControl>
									<FormMessage />
								</FormItem>
							)}
						/>
					</div>

					{employmentType === 'full-time' && (
						<>
							<FormField
								control={form.control}
								name="workSchedule"
								render={({ field }) => (
									<FormItem className="space-y-3">
										<FormLabel>Work Schedule</FormLabel>
										<FormControl>
											<RadioGroup
												onValueChange={field.onChange}
												value={field.value}
												className="flex flex-col space-y-1"
											>
												<FormItem className="flex items-center space-x-3 space-y-0">
													<FormControl>
														<RadioGroupItem value="generic" />
													</FormControl>
													<FormLabel className="font-normal">General</FormLabel>
												</FormItem>
												<FormItem className="flex items-center space-x-3 space-y-0">
													<FormControl>
														<RadioGroupItem value="shifts" />
													</FormControl>
													<FormLabel className="font-normal">Shifts</FormLabel>
												</FormItem>
											</RadioGroup>
										</FormControl>
										<FormDescription>
											Type of work schedule for the employee
										</FormDescription>
										<FormMessage />
									</FormItem>
								)}
							/>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
								<FormField
									control={form.control}
									name="workingDays"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Working Days</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select working days" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{workingDaysOptions.map((option) => (
														<SelectItem key={option.value} value={option.value}>
															{option.label}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>

								<FormField
									control={form.control}
									name="workingHours"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Working Hours</FormLabel>
											<Select
												onValueChange={field.onChange}
												value={field.value}
											>
												<FormControl>
													<SelectTrigger>
														<SelectValue placeholder="Select working hours" />
													</SelectTrigger>
												</FormControl>
												<SelectContent>
													{workingHoursOptions.map((option) => (
														<SelectItem key={option.value} value={option.value}>
															{option.label}
														</SelectItem>
													))}
												</SelectContent>
											</Select>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Conditional rendering based on workSchedule */}
							{workSchedule === 'generic' && (
								<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
									<FormField
										control={form.control}
										name="firstOffDay"
										render={({ field }) => (
											<FormItem>
												<FormLabel>First Off Day</FormLabel>
												<Select
													onValueChange={field.onChange}
													value={field.value}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Select first off day" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														{weekDayOptions.map((option) => (
															<SelectItem
																key={option.value}
																value={option.value}
															>
																{option.label}
															</SelectItem>
														))}
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>

									{workingDays !== '6_DAYS' && (
										<FormField
											control={form.control}
											name="secondOffDay"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Second Off Day</FormLabel>
													<Select
														onValueChange={field.onChange}
														value={field.value}
													>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder="Select second off day" />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															{weekDayOptions.map((option) => (
																<SelectItem
																	key={option.value}
																	value={option.value}
																>
																	{option.label}
																</SelectItem>
															))}
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>
									)}

									{workingDays === '5.5_DAYS' && (
										<FormField
											control={form.control}
											name="halfDay"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Half Day</FormLabel>
													<Select
														onValueChange={field.onChange}
														value={field.value}
													>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder="Select half day option" />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															<SelectItem value="FIRST_HALF">
																First Half
															</SelectItem>
															<SelectItem value="SECOND_HALF">
																Second Half
															</SelectItem>
														</SelectContent>
													</Select>
													<FormDescription>
														Half day work schedule option
													</FormDescription>
													<FormMessage />
												</FormItem>
											)}
										/>
									)}
								</div>
							)}
						</>
					)}

					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<FormField
							control={form.control}
							name="overTimeEligible"
							render={({ field }) => (
								<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
									<div className="space-y-0.5">
										<FormLabel>Overtime Eligible</FormLabel>
										<FormDescription>
											Employee is eligible for overtime compensation
										</FormDescription>
									</div>
									<FormControl>
										<Switch
											checked={field.value}
											onCheckedChange={field.onChange}
										/>
									</FormControl>
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="isBlocked"
							render={({ field }) => (
								<FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
									<div className="space-y-0.5">
										<FormLabel>Restrict Access</FormLabel>
										<FormDescription>
											Check this to restrict the employee from accessing the
											system
										</FormDescription>
									</div>
									<FormControl>
										<Switch
											checked={field.value}
											onCheckedChange={field.onChange}
										/>
									</FormControl>
								</FormItem>
							)}
						/>
					</div>
				</div>

				<div className="space-y-4">
					<h3 className="text-lg font-medium">Equipment Issuance</h3>
					<Separator />

					{equipmentFields.map((field, index) => (
						<Card key={field.id} className="relative">
							<CardContent className="pt-6">
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
									<FormField
										control={form.control}
										name={`equipment.${index}.equipmentName`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Equipment Name</FormLabel>
												<FormControl>
													<Input
														placeholder="Enter equipment name"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`equipment.${index}.brand`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Brand</FormLabel>
												<FormControl>
													<Input placeholder="Enter brand" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`equipment.${index}.model`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Model</FormLabel>
												<FormControl>
													<Input placeholder="Enter model" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`equipment.${index}.serialNumber`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Serial Number</FormLabel>
												<FormControl>
													<Input placeholder="Enter serial number" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`equipment.${index}.assetTag`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Asset Tag</FormLabel>
												<FormControl>
													<Input placeholder="Enter asset tag" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`equipment.${index}.issueDate`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Issue Date</FormLabel>
												<FormControl>
													<Input type="date" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`equipment.${index}.returnDate`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Return Date (Optional)</FormLabel>
												<FormControl>
													<Input type="date" {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
									<FormField
										control={form.control}
										name={`equipment.${index}.issueReason`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Issue Reason</FormLabel>
												<Select
													onValueChange={field.onChange}
													defaultValue={field.value}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Select issue reason" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="new-hire">New Hire</SelectItem>
														<SelectItem value="replacement">
															Replacement
														</SelectItem>
														<SelectItem value="repair">Repair</SelectItem>
														<SelectItem value="other">Other</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>

									{form.watch(`equipment.${index}.issueReason`) === 'other' && (
										<FormField
											control={form.control}
											name={`equipment.${index}.otherIssueReason`}
											render={({ field }) => (
												<FormItem>
													<FormLabel>Specify Other Issue Reason</FormLabel>
													<FormControl>
														<Input placeholder="Enter reason" {...field} />
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									)}

									<FormField
										control={form.control}
										name={`equipment.${index}.returnReason`}
										render={({ field }) => (
											<FormItem>
												<FormLabel>Return Reason (Optional)</FormLabel>
												<Select
													onValueChange={field.onChange}
													defaultValue={field.value}
												>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder="Select return reason" />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														<SelectItem value="damaged">Damaged</SelectItem>
														<SelectItem value="end-of-contract">
															End of Contract
														</SelectItem>
														<SelectItem value="upgrade">Upgrade</SelectItem>
														<SelectItem value="other">Other</SelectItem>
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										)}
									/>

									{form.watch(`equipment.${index}.returnReason`) ===
										'other' && (
										<FormField
											control={form.control}
											name={`equipment.${index}.otherReturnReason`}
											render={({ field }) => (
												<FormItem>
													<FormLabel>Specify Other Return Reason</FormLabel>
													<FormControl>
														<Input placeholder="Enter reason" {...field} />
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									)}
								</div>

								<Button
									type="button"
									variant="ghost"
									size="icon"
									onClick={() => removeEquipment(index)}
									className="absolute top-2 right-2"
								>
									<Trash2 className="h-4 w-4" />
								</Button>
							</CardContent>
						</Card>
					))}

					<Button
						type="button"
						variant="outline"
						size="sm"
						onClick={() =>
							appendEquipment({
								equipmentName: '',
								brand: '',
								model: '',
								serialNumber: '',
								issueDate: new Date().toISOString().split('T')[0],
								returnDate: '',
								issueReason: 'new-hire',
								otherIssueReason: '',
								returnReason: undefined,
								otherReturnReason: '',
								assetTag: '',
							})
						}
					>
						<PlusCircle className="mr-2 h-4 w-4" />
						Add More Equipment
					</Button>
				</div>

				<div className="flex justify-end space-x-4">
					<LoadingSubmitButton
						isLoading={isLoading}
						buttonText={'Save and Next'}
						buttonLoadingText={'Saving...'}
					/>
				</div>
			</form>
		</Form>
	);
}
