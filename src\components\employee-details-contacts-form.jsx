import React, { useState, useEffect } from 'react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
	Edit,
	Loader2,
	Mail,
	Phone,
	Plus,
	Save,
	Trash2,
	User,
	X,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateEmployeeDetailsPersonal } from '@/lib/features/employees/updateEmployeeSlice';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from './ui/select';
import { z } from 'zod';
import { fetchDialCodes } from '@/lib/features/location/locationSlice';

// Define the schema for contact details
const contactSchema = z
	.object({
		type: z.enum(['emergency', 'reference'], {
			errorMap: () => ({
				message: "Contact type must be either 'emergency' or 'reference'",
			}),
		}),
		name: z.string().min(1, 'Name is required'),
		relationship: z.string().min(1, 'Relationship is required'),
		countryDialCode: z.string().min(1, 'Country dial code is required'),
		phone: z.string().min(1, 'Phone number is required'),
		email: z.string().email('Invalid email address'),
		_id: z.string().optional(),
	})
	.superRefine((data, ctx) => {
		if (data.type === 'emergency' && !data.name) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'Name is required for emergency contacts',
				path: ['name'],
			});
		}
		if (data.type === 'reference' && !data.email) {
			ctx.addIssue({
				code: z.ZodIssueCode.custom,
				message: 'Email is required for reference contacts',
				path: ['email'],
			});
		}
	});

const contactsSchema = z.object({
	contacts: z.array(contactSchema).min(1, 'At least one contact is required'),
});

const EmployeeDetailsContactsForm = ({ employeeId, contactDetails = [] }) => {
	const { isLoading } = useAppSelector((store) => store.employee);
	const { dialCodes } = useAppSelector((store) => store.location);
	const dispatch = useAppDispatch();
	const [isEditing, setIsEditing] = useState(false);

	useEffect(() => {
		dispatch(fetchDialCodes());
	}, [dispatch]);

	const form = useForm({
		resolver: zodResolver(contactsSchema),
		defaultValues: {
			contacts:
				contactDetails.length > 0
					? contactDetails
					: [
							{
								type: 'emergency',
								name: '',
								relationship: '',
								countryDialCode: '+1',
								phone: '',
								email: '',
							},
						],
		},
	});

	const {
		fields: contactFields,
		append: appendContact,
		remove: removeContact,
	} = useFieldArray({
		control: form.control,
		name: 'contacts',
	});

	const onSubmit = async (data) => {
		console.log('Form data to submit:', data);

		// Format the data for API
		const formattedData = {
			contactDetails: data.contacts,
			employeeId,
		};

		// Call the API to update contact details
		const result = await dispatch(
			updateEmployeeDetailsPersonal({
				employeeId,
				...formattedData,
			})
		);

		// Check if update was successful
		if (updateEmployeeDetailsPersonal.fulfilled.match(result)) {
			setIsEditing(false);
		}
	};

	const addNewContact = () => {
		appendContact({
			type: 'emergency',
			name: '',
			relationship: '',
			countryDialCode: '+1',
			phone: '',
			email: '',
		});
	};

	return (
		<>
			{/* Edit Controls */}
			<div className="flex justify-end mb-4">
				<div className="flex gap-2">
					{isEditing && (
						<Button
							className="bg-red-600 text-white"
							variant="outline"
							onClick={() => setIsEditing(false)}
							disabled={isLoading}
						>
							{<X className="h-4 w-4 mr-2" size={16} />}Cancel
						</Button>
					)}
					<Button
						variant="default"
						onClick={() =>
							isEditing ? form.handleSubmit(onSubmit)() : setIsEditing(true)
						}
						disabled={isLoading}
					>
						{isLoading ? (
							<Loader2 className="animate-spin mr-2" size={16} />
						) : isEditing ? (
							<Save className="h-4 w-4 mr-2" size={16} />
						) : (
							<Edit className="h-4 w-4 mr-2" size={16} />
						)}
						{isEditing ? 'Save Changes' : 'Edit Contact Information'}
					</Button>
				</div>
			</div>

			<Card>
				<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
					<CardTitle>Contact Information</CardTitle>
					{isEditing && (
						<Button
							type="button"
							variant="outline"
							size="sm"
							onClick={addNewContact}
						>
							<Plus className="h-4 w-4 mr-2" />
							Add Contact
						</Button>
					)}
				</CardHeader>
				<CardContent>
					{isEditing ? (
						<Form {...form}>
							{contactFields.length > 0 ? (
								<div className="space-y-6">
									{contactFields.map((field, index) => (
										<div
											key={field.id}
											className="space-y-4 p-4 border rounded-md relative"
										>
											<Button
												type="button"
												variant="ghost"
												size="icon"
												className="absolute top-2 right-2 h-6 w-6 text-destructive"
												onClick={() => removeContact(index)}
											>
												<Trash2 className="h-4 w-4" />
											</Button>

											<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
												<FormField
													control={form.control}
													name={`contacts.${index}.type`}
													render={({ field }) => (
														<FormItem>
															<FormLabel className="text-sm font-medium text-muted-foreground">
																Contact Type
															</FormLabel>
															<Select
																onValueChange={field.onChange}
																defaultValue={field.value}
															>
																<FormControl>
																	<SelectTrigger>
																		<SelectValue placeholder="Select contact type" />
																	</SelectTrigger>
																</FormControl>
																<SelectContent>
																	<SelectItem value="emergency">
																		Emergency
																	</SelectItem>
																	<SelectItem value="reference">
																		Reference
																	</SelectItem>
																</SelectContent>
															</Select>
															<FormMessage />
														</FormItem>
													)}
												/>

												<FormField
													control={form.control}
													name={`contacts.${index}.name`}
													render={({ field }) => (
														<FormItem>
															<FormLabel className="text-sm font-medium text-muted-foreground">
																Name
															</FormLabel>
															<FormControl>
																<Input
																	placeholder="Enter contact name"
																	{...field}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												<FormField
													control={form.control}
													name={`contacts.${index}.relationship`}
													render={({ field }) => (
														<FormItem>
															<FormLabel className="text-sm font-medium text-muted-foreground">
																Relationship
															</FormLabel>
															<FormControl>
																<Input
																	placeholder="Enter relationship"
																	{...field}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												<div className="flex gap-2">
													<FormField
														control={form.control}
														name={`contacts.${index}.countryDialCode`}
														render={({ field }) => (
															<FormItem className="w-1/3">
																<FormLabel className="text-sm font-medium text-muted-foreground">
																	Code
																</FormLabel>
																<Select
																	onValueChange={field.onChange}
																	defaultValue={field.value}
																>
																	<FormControl>
																		<SelectTrigger>
																			<SelectValue placeholder="Code" />
																		</SelectTrigger>
																	</FormControl>
																	<SelectContent className="max-h-[200px]">
																		{dialCodes?.map((dialCode) => (
																			<SelectItem
																				key={dialCode.code}
																				value={dialCode.code}
																			>
																				{dialCode.code} ({dialCode.country})
																			</SelectItem>
																		))}
																	</SelectContent>
																</Select>
																<FormMessage />
															</FormItem>
														)}
													/>

													<FormField
														control={form.control}
														name={`contacts.${index}.phone`}
														render={({ field }) => (
															<FormItem className="flex-1">
																<FormLabel className="text-sm font-medium text-muted-foreground">
																	Phone Number
																</FormLabel>
																<FormControl>
																	<Input
																		placeholder="Enter phone number"
																		{...field}
																	/>
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>
												</div>

												<FormField
													control={form.control}
													name={`contacts.${index}.email`}
													render={({ field }) => (
														<FormItem className="md:col-span-2">
															<FormLabel className="text-sm font-medium text-muted-foreground">
																Email
															</FormLabel>
															<FormControl>
																<Input
																	placeholder="Enter email address"
																	{...field}
																/>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>
											</div>
										</div>
									))}
								</div>
							) : (
								<div className="flex flex-col items-center justify-center py-6 text-center">
									<User className="h-8 w-8 text-muted-foreground mb-2" />
									<p className="text-muted-foreground">No contacts added yet</p>
									<Button
										type="button"
										variant="outline"
										size="sm"
										className="mt-4"
										onClick={addNewContact}
									>
										<Plus className="h-4 w-4 mr-2" />
										Add Contact
									</Button>
								</div>
							)}
						</Form>
					) : (
						<>
							{contactDetails.length > 0 ? (
								<div className="space-y-6">
									{contactDetails.map((contact, index) => (
										<div key={contact._id || index} className="space-y-2">
											<div className="flex justify-between items-center">
												<h4 className="font-medium">{contact.name}</h4>
												<Badge className="capitalize">{contact.type}</Badge>
											</div>
											<p className="text-sm text-muted-foreground">
												Relationship: {contact.relationship}
											</p>
											<p className="text-sm">
												<Phone className="h-4 w-4 inline-block mr-2" />
												{contact.countryDialCode} {contact.phone}
											</p>
											<p className="text-sm">
												<Mail className="h-4 w-4 inline-block mr-2" />
												{contact.email}
											</p>
											{index < contactDetails.length - 1 && (
												<Separator className="my-4" />
											)}
										</div>
									))}
								</div>
							) : (
								<div className="flex items-center justify-center p-6 text-muted-foreground">
									<p>No contact information available</p>
								</div>
							)}
						</>
					)}
				</CardContent>
			</Card>
		</>
	);
};

export default EmployeeDetailsContactsForm;
