import { <PERSON><PERSON>, <PERSON>alogContent, <PERSON>alogDes<PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { timeAgo } from './utils/dateUtils';

// Helper function to capitalize strings
const capitalize = (str) => {
	if (!str) return '';
	return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

// Helper function to format data for display
const formatDataForDisplay = (data) => {
	if (!data) return 'N/A';
	if (typeof data === 'string') return data;
	if (typeof data === 'object') {
		// Handle arrays
		if (Array.isArray(data)) {
			return data.length > 0 ? `${data.length} items` : 'No items';
		}
		// Handle objects - show key-value pairs
		const entries = Object.entries(data);
		if (entries.length === 0) return 'No data';
		if (entries.length === 1) {
			const [key, value] = entries[0];
			return `${key}: ${value}`;
		}
		return `${entries.length} fields updated`;
	}
	return String(data);
};

export function CancelRequestDialog({ 
	cancelRequestId, 
	pendingChanges, 
	onCancel, 
	onConfirm 
}) {
	const requestToCancel = pendingChanges.find((c) => c._id === cancelRequestId);

	return (
		<Dialog
			open={cancelRequestId !== null}
			onOpenChange={(open) => !open && onCancel()}
		>
			<DialogContent className="max-w-md">
				<DialogHeader>
					<DialogTitle>Cancel Request</DialogTitle>
					<DialogDescription>
						Are you sure you want to cancel this request? This action cannot
						be undone.
					</DialogDescription>
				</DialogHeader>
				<div className="py-4">
					{requestToCancel && (
						<div className="p-3 bg-slate-50 rounded-md border">
							<p className="font-medium">
								{capitalize(
									requestToCancel.section?.replace('-', ' ') || 'Unknown'
								)}{' '}
								-{' '}
								{requestToCancel.field || 'Field Update'}
							</p>
							<div className="mt-1 text-sm text-muted-foreground">
								<p>
									Requested change:{' '}
									{formatDataForDisplay(requestToCancel.newData)}
								</p>
								<p className="mt-1">
									Submitted{' '}
									{timeAgo(requestToCancel.submittedAt)}
								</p>
							</div>
						</div>
					)}
				</div>
				<DialogFooter>
					<Button variant="outline" onClick={onCancel}>
						Keep Request
					</Button>
					<Button
						variant="destructive"
						onClick={() => onConfirm(cancelRequestId)}
					>
						Cancel Request
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
