import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { customFetch, showErrors } from '@/lib/utils';
import { toast } from 'sonner';

const initialState = {
	editRequests: [],
	isLoading: false,
};

export const fetchEditRequests = createAsyncThunk(
	'editRequest/fetchEditRequests',
	async ({ userId, section, status } = {}, thunkAPI) => {
		try {
			let query = '/employees/edit-request';
			const queryParams = [];
			
			if (section) queryParams.push(`section=${section}`);
			if (status) queryParams.push(`status=${status}`);
			if (userId) queryParams.push(`userId=${userId}`);
			
			if (queryParams.length > 0) {
				query += `?${queryParams.join('&')}`;
			}
			
			const { data } = await customFetch.get(query);
			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const addEditRequest = createAsyncThunk(
	'editRequest/addEditRequest',
	async (editRequestDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.post(
				'/employees/edit-request',
				editRequestDetails
			);

			if (data?.success) {
				thunkAPI.dispatch(fetchEditRequests());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateEditRequest = createAsyncThunk(
	'editRequest/updateEditRequest',
	async (editRequestDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/employees/edit-request',
				editRequestDetails
			);

			if (data?.success) {
				thunkAPI.dispatch(fetchEditRequests());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const updateEditRequestStatus = createAsyncThunk(
	'editRequest/updateEditRequestStatus',
	async (editRequestDetails, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/employees/edit-request/status',
				editRequestDetails
			);

			if (data?.success) {
				thunkAPI.dispatch(fetchEditRequests());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

export const deleteEditRequests = createAsyncThunk(
	'editRequest/deleteEditRequests',
	async (editRequestIds, thunkAPI) => {
		try {
			const { data } = await customFetch.patch(
				'/employees/edit-request/remove',
				{ editRequestIds }
			);

			if (data?.success) {
				thunkAPI.dispatch(fetchEditRequests());
			}

			return data;
		} catch (error) {
			return thunkAPI.rejectWithValue(error.response.data);
		}
	}
);

const editRequestSlice = createSlice({
	name: 'editRequest',
	initialState,
	reducers: {},
	extraReducers: (builder) => {
		builder
			.addCase(fetchEditRequests.pending, (state) => {
				state.isLoading = true;
			})
			.addCase(fetchEditRequests.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				state.editRequests = payload.data.editRequests;
			})
			.addCase(fetchEditRequests.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(addEditRequest.pending, (state) => {
				state.isLoading = true;
				toast.info('Creating Edit Request...');
			})
			.addCase(addEditRequest.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(addEditRequest.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateEditRequest.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Edit Request...');
			})
			.addCase(updateEditRequest.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateEditRequest.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(updateEditRequestStatus.pending, (state) => {
				state.isLoading = true;
				toast.info('Updating Edit Request Status...');
			})
			.addCase(updateEditRequestStatus.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(updateEditRequestStatus.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			})
			.addCase(deleteEditRequests.pending, (state) => {
				state.isLoading = true;
				toast.info('Deleting Edit Request...');
			})
			.addCase(deleteEditRequests.fulfilled, (state, { payload }) => {
				state.isLoading = false;
				toast.success(payload.message);
			})
			.addCase(deleteEditRequests.rejected, (state, { payload }) => {
				state.isLoading = false;
				showErrors(payload);
			});
	},
});

// export const {} = editRequestSlice.actions;
export default editRequestSlice.reducer;
