// Helper function to convert ISO date to YYYY-MM-DD format for frontend display
export const convertIsoToDateInput = (isoDate) => {
	if (!isoDate) return '';
	try {
		// Handle both ISO string and Date object
		const date = new Date(isoDate);
		if (isNaN(date.getTime())) return '';
		
		// Convert to YYYY-MM-DD format
		const year = date.getFullYear();
		const month = String(date.getMonth() + 1).padStart(2, '0');
		const day = String(date.getDate()).padStart(2, '0');
		
		return `${year}-${month}-${day}`;
	} catch (error) {
		console.error('Error converting ISO date to input format:', error);
		return '';
	}
};

// Helper function to convert YYYY-MM-DD format to ISO string for payload
export const convertDateInputToIso = (dateInput) => {
	if (!dateInput) return '';
	try {
		// Create date object from YYYY-MM-DD string
		const date = new Date(dateInput + 'T00:00:00.000Z');
		if (isNaN(date.getTime())) return '';
		
		// Return ISO string
		return date.toISOString();
	} catch (error) {
		console.error('Error converting date input to ISO:', error);
		return '';
	}
};

// Helper function to format date for display
export const formatDate = (dateString) => {
	if (!dateString) return 'N/A';
	try {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'short',
			day: 'numeric',
		});
	} catch (error) {
		return 'Invalid Date';
	}
};

// Helper function to calculate time ago
export const timeAgo = (dateString) => {
	if (!dateString) return 'Unknown';
	
	try {
		const date = new Date(dateString);
		const now = new Date();
		const diffInMs = now - date;
		const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
		const diffInHours = Math.floor(diffInMs / (1000 * 60 * 60));
		const diffInDays = Math.floor(diffInMs / (1000 * 60 * 60 * 24));

		if (diffInMinutes < 1) return 'Just now';
		if (diffInMinutes < 60) return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
		if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
		if (diffInDays < 7) return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
		
		return formatDate(dateString);
	} catch (error) {
		return 'Invalid Date';
	}
};
