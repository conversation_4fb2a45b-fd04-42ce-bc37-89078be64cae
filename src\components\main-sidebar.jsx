import { AppSidebar } from '@/components/app-sidebar';
import {
	Bread<PERSON>rumb,
	BreadcrumbItem,
	BreadcrumbLink,
	BreadcrumbList,
	BreadcrumbPage,
	BreadcrumbSeparator,
} from '@/components/ui/breadcrumb';
import { Separator } from '@/components/ui/separator';
import {
	SidebarInset,
	SidebarProvider,
	SidebarTrigger,
} from '@/components/ui/sidebar';
import { ResponsiveBreadcrumb } from './responsive-breadcrumbs';
import { usePathname } from 'next/navigation';
import { SuperAdminAppSidebar } from './super-admin-app-sidebar';
import { userRoles } from '@/lib/utils';
import { ModeToggle } from './theme-provider';

export function MainSidebar({ children, role }) {
	const pathname = usePathname();
	const segments = pathname.replace(/^\//, '').split('/');

	return (
		<SidebarProvider
			style={{
				'--sidebar-width': '300px',
			}}
			defaultOpen={false}
		>
			{role.includes(userRoles.SUPER_ADMIN) ? (
				<SuperAdminAppSidebar />
			) : role.includes(
					userRoles.CLIENT_ADMIN || userRoles.GLORIFIED_CLIENT_ADMIN
			  ) ? (
				<AppSidebar />
			) : null}
			<SidebarInset>
				<header className="sticky top-0 z-50 flex shrink-0 items-center gap-2 shadow bg-background justify-between py-3 px-4 max-h-15 rounded-t-xl">
					<div className="flex items-center gap-2 px-4 capitalize">
						<SidebarTrigger className="-ml-1" />
						<Separator orientation="vertical" className="mr-2 h-4" />
						<ResponsiveBreadcrumb segments={segments} />
					</div>
					<ModeToggle />
				</header>
				<div
					style={{
						maxHeight: 'calc(100vh - 5rem)',
						overflow: 'auto',
					}}
					className="flex flex-1 flex-col gap-4 p-4 "
				>
					{children}
				</div>
			</SidebarInset>
		</SidebarProvider>
	);
}
