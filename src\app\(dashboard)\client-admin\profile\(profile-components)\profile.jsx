'use client';
import { useEffect } from 'react';
import ProfileCard from './profile-card';
import Mark<PERSON><PERSON>dance from './mark-attendance';
import LeaveCard from './leave-card';
import TaskCard from './task-card';
import PlanCard from './plan-card';
import NotificationCard from './notification-card';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { fetchProfilePageDetails } from '@/lib/features/employees/employeeSlice';
import CalendarCard from './calendar-card';

export function Profile() {
	const dispatch = useAppDispatch();
	const { authenticatedUser } = useAppSelector((store) => store.auth);
	const { userProfile } = useAppSelector((store) => store.employee);

	useEffect(() => {
		if (authenticatedUser?.userId) {
			dispatch(fetchProfilePageDetails(authenticatedUser.userId));
		}
	}, [dispatch, authenticatedUser?.userId]);

	return (
		<div className="w-full h-full flex flex-col gap-4 max-w-[1600px] mx-auto">
			<div className="grid grid-cols-1 lg:grid-cols-[auto_1fr] gap-4 h-full">
				<div className="w-full lg:w-auto">
					<ProfileCard userProfile={userProfile} />
				</div>

				<div className="flex flex-col gap-4 min-w-0">
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4 h-full">
						<div className="bg-gray-50 rounded-lg shadow-sm h-full flex flex-col">
							<MarkAttendance className="flex-1" />
						</div>
						<div className="bg-gray-50 rounded-lg shadow-sm flex flex-col gap-4">
							<div className="flex-1">
								<CalendarCard />
							</div>
							<div className="flex-1">
								<LeaveCard />
							</div>
						</div>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 gap-4 flex-1">
						<div className="bg-gray-50 rounded-lg shadow-sm h-full">
							<TaskCard />
						</div>
						<div className="flex flex-col gap-4 h-full">
							<div className="bg-gray-50 rounded-lg shadow-sm flex-1">
								<PlanCard />
							</div>
							<div className="bg-gray-50 rounded-lg shadow-sm flex-1">
								<NotificationCard />
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
