import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Mail, Phone, Calendar } from 'lucide-react';
import { formatDate } from './utils/dateUtils';

// Helper function to capitalize strings
const capitalize = (str) => {
	if (!str) return '';
	return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

export function ProfileHeader({ personalDetails, experience, data }) {
	return (
		<div className="relative mb-8 bg-gradient-to-r from-purple-500 to-indigo-600 rounded-xl overflow-hidden">
			<div className="absolute inset-0 bg-black opacity-10 z-0"></div>
			<div className="relative z-10 p-8 flex flex-col md:flex-row items-center gap-6 text-white">
				<div className="relative">
					<Avatar className="h-32 w-32 border-4 border-white shadow-lg">
						<AvatarImage
							src={personalDetails?.profilePhoto || '/placeholder.svg'}
							alt={personalDetails?.nameOnNRIC || 'User'}
						/>
						<AvatarFallback className="text-3xl bg-indigo-700">
							{personalDetails?.nameOnNRIC
								? personalDetails.nameOnNRIC
										.split(' ')
										.map((n) => n[0])
										.join('')
								: 'U'}
						</AvatarFallback>
					</Avatar>
					<Badge className="absolute -bottom-2 right-0 bg-green-500 border-2 border-white">
						Active
					</Badge>
				</div>

				<div className="text-center md:text-left">
					<h1 className="text-3xl font-bold">
						{personalDetails?.nameOnNRIC || 'User Name'}
					</h1>
					<div className="flex flex-wrap justify-center md:justify-start gap-2 mt-2">
						<Badge
							variant="outline"
							className="bg-white/20 text-white border-white/40"
						>
							ID: {personalDetails?.employeeOrgId || 'N/A'}
						</Badge>
						<Badge
							variant="outline"
							className="bg-white/20 text-white border-white/40"
						>
							{experience && experience.length > 0
								? experience[0]?.designation
								: 'N/A'}
						</Badge>
						<Badge
							variant="outline"
							className="bg-white/20 text-white border-white/40"
						>
							{capitalize(personalDetails?.nationality || 'N/A')}
						</Badge>
					</div>
					<div className="mt-4 flex flex-wrap justify-center md:justify-start gap-4">
						<div className="flex items-center gap-1">
							<Mail className="h-4 w-4" />
							<span>{data?.email || 'N/A'}</span>
						</div>
						<div className="flex items-center gap-1">
							<Phone className="h-4 w-4" />
							<span>
								{personalDetails?.countryDialCode || ''}{' '}
								{personalDetails?.mobile || 'N/A'}
							</span>
						</div>
						<div className="flex items-center gap-1">
							<Calendar className="h-4 w-4" />
							<span>
								Joined: {formatDate(personalDetails?.dateOfJoining)}
							</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
