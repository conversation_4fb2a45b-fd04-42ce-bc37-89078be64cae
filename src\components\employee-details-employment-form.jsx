import React, { useState, useEffect } from 'react';
import { useForm, useWatch } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
	Briefcase,
	Building,
	Calendar,
	Edit,
	Loader2,
	Mail,
	Phone,
	Save,
	X,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { updateEmployeeDetailsPersonal } from '@/lib/features/employees/updateEmployeeSlice';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from './ui/select';
import { Switch } from '@/components/ui/switch';
import { z } from 'zod';
import {
	fetchBusinessUnitDepartments,
	fetchBusinessUnits,
} from '@/lib/features/company-infrastructure/businessUnitSlice';
import { fetchDepartmentDesignations } from '@/lib/features/company-infrastructure/departmentSlice';

// Define the schema for employment details
const employmentSchema = z.object({
	source: z.enum(['staff-recommendation', 'job-advertisement'], {
		errorMap: () => ({
			message:
				"Source must be either 'staff-recommendation' or 'job-advertisement'",
		}),
	}),
	businessUnit: z.string().min(1, 'Business unit is required'),
	department: z.string().min(1, 'Department is required'),
	designation: z.string().min(1, 'Designation is required'),
	employmentType: z.enum(['part-time', 'full-time'], {
		errorMap: () => ({
			message: "Employment type must be either 'part-time' or 'full-time'",
		}),
	}),
	probationPeriod: z.string().min(1, 'Probation period is required'),
	overTimeEligible: z.boolean().default(false),
	workSchedule: z.enum(['shifts', 'generic']).optional(),
	workingDays: z
		.enum(['5_DAYS', '5.5_DAYS', '6_DAYS', 'ALTERNATE_SATURDAYS'])
		.optional(),
	workingHours: z.enum(['4', '6', '8', '10', '12']).optional(),
	firstOffDay: z.string().optional(),
	secondOffDay: z.string().optional(),
	halfDay: z.enum(['FIRST_HALF', 'SECOND_HALF']).optional(),
});

// Helper functions for formatting
const formatWorkingDays = (days) => {
	if (!days) return 'N/A';

	const workingDaysMap = {
		'5_DAYS': '5 Days',
		'5.5_DAYS': '5.5 Days',
		'6_DAYS': '6 Days',
		ALTERNATE_SATURDAYS: 'Alternate Saturdays',
	};

	return workingDaysMap[days] || days;
};

const formatHalfDay = (halfDay) => {
	if (!halfDay) return 'N/A';

	const halfDayMap = {
		FIRST_HALF: 'First Half',
		SECOND_HALF: 'Second Half',
	};

	return halfDayMap[halfDay] || halfDay;
};

const formatOffDay = (day) => {
	if (!day) return 'N/A';
	return day.toLowerCase().replace(/_/g, ' ');
};

const EmployeeDetailsEmploymentForm = ({
	employeeId,
	employmentDetails = null,
	employee = {},
}) => {
	const { isLoading } = useAppSelector((store) => store.employee);
	const { businessUnits } = useAppSelector((store) => store.businessUnit);
	const { departments } = useAppSelector((store) => store.department);
	const { designations } = useAppSelector((store) => store.designation);
	const dispatch = useAppDispatch();
	const [isEditing, setIsEditing] = useState(false);
	const [originalFormValues, setOriginalFormValues] = useState(null);
	const [isInitialLoad, setIsInitialLoad] = useState(true);

	// Default values for when employmentDetails is null
	const defaultValues = {
		source: 'job-advertisement',
		businessUnit: '',
		department: '',
		designation: '',
		employmentType: 'full-time',
		probationPeriod: '3',
		overTimeEligible: false,
		workSchedule: 'generic',
		workingDays: '5_DAYS',
		workingHours: '8',
		firstOffDay: 'SATURDAY',
		secondOffDay: 'SUNDAY',
		halfDay: undefined,
	};

	// Use employmentDetails if available, otherwise use default values
	const formValues = employmentDetails
		? {
				source: employmentDetails.source || defaultValues.source,
				businessUnit: employmentDetails.businessUnit?._id || '',
				department: employmentDetails.department?._id || '',
				designation: employmentDetails.designation?._id || '',
				employmentType:
					employmentDetails.employmentType || defaultValues.employmentType,
				probationPeriod:
					employmentDetails.probationPeriod?.toString() ||
					defaultValues.probationPeriod,
				overTimeEligible:
					employmentDetails.overTimeEligible || defaultValues.overTimeEligible,
				workSchedule:
					employmentDetails.workSchedule || defaultValues.workSchedule,
				workingDays: employmentDetails.workingDays || defaultValues.workingDays,
				workingHours:
					employmentDetails.workingHours || defaultValues.workingHours,
				firstOffDay: employmentDetails.firstOffDay || defaultValues.firstOffDay,
				secondOffDay:
					employmentDetails.secondOffDay || defaultValues.secondOffDay,
				halfDay: employmentDetails.halfDay || defaultValues.halfDay,
			}
		: defaultValues;

	const form = useForm({
		resolver: zodResolver(employmentSchema),
		defaultValues: formValues,
	});

	const watchWorkingDays = form.watch('workingDays');
	const watchWorkSchedule = form.watch('workSchedule');

	const selectedBusinessUnit = useWatch({
		control: form.control,
		name: 'businessUnit',
	});

	const selectedDepartment = useWatch({
		control: form.control,
		name: 'department',
	});

	useEffect(() => {
		dispatch(fetchBusinessUnits());
	}, [dispatch]);

	// Effect to handle initial data loading
	useEffect(() => {
		if (employmentDetails && isInitialLoad) {
			// Load departments for the initial business unit
			if (employmentDetails.businessUnit?._id) {
				dispatch(fetchBusinessUnitDepartments(employmentDetails.businessUnit._id));
			}
			// Load designations for the initial department
			if (employmentDetails.department?._id) {
				dispatch(fetchDepartmentDesignations(employmentDetails.department._id));
			}
			// Set initial load to false after a short delay to ensure data is loaded
			setTimeout(() => {
				setIsInitialLoad(false);
			}, 100);
		} else if (!employmentDetails && isInitialLoad) {
			// If no employment details exist, set initial load to false immediately
			setIsInitialLoad(false);
		}
	}, [employmentDetails, isInitialLoad, dispatch]);

	useEffect(() => {
		console.log(selectedBusinessUnit)
		if (selectedBusinessUnit) {
			dispatch(fetchBusinessUnitDepartments(selectedBusinessUnit));
		}
	}, [selectedBusinessUnit, dispatch, businessUnits]);

	useEffect(() => {
		if (selectedDepartment) {
			dispatch(fetchDepartmentDesignations(selectedDepartment));
		}
	}, [selectedDepartment, dispatch]);

	// Clear designation when department changes (only after initial load)
	useEffect(() => {
		if (selectedDepartment && !isInitialLoad) {
			form.setValue('designation', '');
		}
	}, [selectedDepartment, form, isInitialLoad]);

	// Clear department and designation when business unit changes (only after initial load)
	useEffect(() => {
		if (selectedBusinessUnit && !isInitialLoad) {
			form.setValue('department', '');
			form.setValue('designation', '');
		}
	}, [selectedBusinessUnit, form, isInitialLoad]);

	const onSubmit = async (data) => {
		console.log('Form data to submit:', data);

		// Format the data for API
		const formattedData = {
			employmentDetails: {
				...data,
			},
			employeeId,
		};

		// Call the API to update employment details
		const result = await dispatch(
			updateEmployeeDetailsPersonal({
				employeeId,
				...formattedData,
			})
		);

		// Check if update was successful
		if (updateEmployeeDetailsPersonal.fulfilled.match(result)) {
			setIsEditing(false);
		}
	};

	// Options for dropdowns
	const sourceOptions = [
		{ value: 'staff-recommendation', label: 'Staff Recommendation' },
		{ value: 'job-advertisement', label: 'Job Advertisement' },
	];

	const employmentTypeOptions = [
		{ value: 'full-time', label: 'Full Time' },
		{ value: 'part-time', label: 'Part Time' },
	];

	const workScheduleOptions = [
		{ value: 'generic', label: 'Generic' },
		{ value: 'shifts', label: 'Shifts' },
	];

	const workingDaysOptions = [
		{ value: '5_DAYS', label: '5 Days' },
		{ value: '5.5_DAYS', label: '5.5 Days' },
		{ value: '6_DAYS', label: '6 Days' },
		{ value: 'ALTERNATE_SATURDAYS', label: 'Alternate Saturdays' },
	];

	const workingHoursOptions = [
		{ value: '4', label: '4 Hours' },
		{ value: '6', label: '6 Hours' },
		{ value: '8', label: '8 Hours' },
		{ value: '10', label: '10 Hours' },
		{ value: '12', label: '12 Hours' },
	];

	const weekDayOptions = [
		{ value: 'MONDAY', label: 'Monday' },
		{ value: 'TUESDAY', label: 'Tuesday' },
		{ value: 'WEDNESDAY', label: 'Wednesday' },
		{ value: 'THURSDAY', label: 'Thursday' },
		{ value: 'FRIDAY', label: 'Friday' },
		{ value: 'SATURDAY', label: 'Saturday' },
		{ value: 'SUNDAY', label: 'Sunday' },
	];

	const halfDayOptions = [
		{ value: 'FIRST_HALF', label: 'First Half' },
		{ value: 'SECOND_HALF', label: 'Second Half' },
	];

	const probationPeriodOptions = [
		{ value: '1', label: '1 Month' },
		{ value: '2', label: '2 Months' },
		{ value: '3', label: '3 Months' },
		{ value: '6', label: '6 Months' },
		{ value: '12', label: '12 Months' },
	];

	return (
		<>
			{/* Edit Controls */}
			<div className="flex justify-end mb-4">
				<div className="flex gap-2">
					{isEditing && (
						<Button
							className="bg-red-600 text-white"
							variant="outline"
							onClick={() => {
								// Reset form to original values
								if (originalFormValues) {
									console.log(
										'Restoring original form values:',
										originalFormValues
									);
									form.reset(originalFormValues);
								}
								setIsEditing(false);
								// Reset initial load flag to prevent clearing values on cancel
								setIsInitialLoad(true);
								setTimeout(() => setIsInitialLoad(false), 100);
							}}
							disabled={isLoading}
						>
							{<X className="h-4 w-4 mr-2" size={16} />}Cancel
						</Button>
					)}
					<Button
						variant="default"
						onClick={() => {
							if (isEditing) {
								form.handleSubmit(onSubmit)();
							} else {
								// Save original form values before entering edit mode
								const currentValues = form.getValues();
								console.log('Saving original form values:', currentValues);
								setOriginalFormValues(currentValues);
								setIsEditing(true);
							}
						}}
						disabled={isLoading}
					>
						{isLoading ? (
							<Loader2 className="animate-spin mr-2" size={16} />
						) : isEditing ? (
							<Save className="h-4 w-4 mr-2" size={16} />
						) : (
							<Edit className="h-4 w-4 mr-2" size={16} />
						)}
						{isEditing ? 'Save Changes' : 'Edit Employment Details'}
					</Button>
				</div>
			</div>

			{employmentDetails ? (
				<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
					{/* Position Information Card */}
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle>Position Information</CardTitle>
						</CardHeader>
						<CardContent>
							{isEditing ? (
								<Form {...form}>
									<div className="grid grid-cols-1 md:grid-cols-2 gap-4 ">
										<FormField
											control={form.control}
											name="source"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Source
													</FormLabel>
													<Select
														onValueChange={field.onChange}
														defaultValue={field.value}
													>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder="Select source" />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															{sourceOptions.map((option) => (
																<SelectItem
																	key={option.value}
																	value={option.value}
																>
																	{option.label}
																</SelectItem>
															))}
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="businessUnit"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Business Unit</FormLabel>
													<Select
														onValueChange={field.onChange}
														value={field.value}
													>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder="Select business unit" />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															{businessUnits?.map((unit) => (
																<SelectItem key={unit._id} value={unit._id}>
																	{unit.name} ({unit.location})
																</SelectItem>
															))}
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>
										<FormField
											control={form.control}
											name="department"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Department</FormLabel>
													<Select
														onValueChange={field.onChange}
														value={field.value}
														disabled={
															!selectedBusinessUnit || departments.length === 0
														}
													>
														<FormControl>
															<SelectTrigger>
																<SelectValue
																	placeholder={
																		selectedBusinessUnit
																			? 'Select department'
																			: 'Select business unit first'
																	}
																/>
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															{departments.map((dept) => (
																<SelectItem key={dept._id} value={dept._id}>
																	{dept.name}
																</SelectItem>
															))}
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>
										<FormField
											control={form.control}
											name="designation"
											render={({ field }) => (
												<FormItem>
													<FormLabel>Designation</FormLabel>
													<Select
														onValueChange={field.onChange}
														value={field.value}
														disabled={
															!selectedDepartment || designations.length === 0
														}
													>
														<FormControl>
															<SelectTrigger>
																<SelectValue
																	placeholder={
																		selectedDepartment
																			? 'Select designation'
																			: 'Select department first'
																	}
																/>
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															{designations?.map((designation) => (
																<SelectItem
																	key={designation._id}
																	value={designation._id}
																>
																	{designation.name}
																</SelectItem>
															))}
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="employmentType"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Employment Type
													</FormLabel>
													<Select
														onValueChange={field.onChange}
														defaultValue={field.value}
													>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder="Select employment type" />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															{employmentTypeOptions.map((option) => (
																<SelectItem
																	key={option.value}
																	value={option.value}
																>
																	{option.label}
																</SelectItem>
															))}
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="probationPeriod"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Probation Period
													</FormLabel>
													<Select
														onValueChange={field.onChange}
														defaultValue={field.value}
													>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder="Select probation period" />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															{probationPeriodOptions.map((option) => (
																<SelectItem
																	key={option.value}
																	value={option.value}
																>
																	{option.label}
																</SelectItem>
															))}
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name="overTimeEligible"
											render={({ field }) => (
												<FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
													<div className="space-y-0.5">
														<FormLabel>Overtime Eligible</FormLabel>
													</div>
													<FormControl>
														<Switch
															checked={field.value}
															onCheckedChange={field.onChange}
														/>
													</FormControl>
												</FormItem>
											)}
										/>
									</div>
								</Form>
							) : (
								<div className="grid grid-cols-2 gap-4">
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Source
										</p>
										<p className="capitalize">
											{employmentDetails.source?.replace(/-/g, ' ')}
										</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Business Unit
										</p>
										<p>{employmentDetails.businessUnit?.name}</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Department
										</p>
										<p>{employmentDetails.department?.name}</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Designation
										</p>
										<p>{employmentDetails.designation?.name}</p>
									</div>

									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Employment Type
										</p>
										<p className="capitalize">
											{employmentDetails.employmentType?.replace(/-/g, ' ')}
										</p>
									</div>

									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Probation Period
										</p>
										<p>{employmentDetails.probationPeriod} months</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Overtime Eligible
										</p>
										<p>{employmentDetails.overTimeEligible ? 'Yes' : 'No'}</p>
									</div>
								</div>
							)}
						</CardContent>
					</Card>

					{/* Work Schedule Card */}
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle>Work Schedule</CardTitle>
						</CardHeader>
						<CardContent>
							{isEditing ? (
								<Form {...form}>
									<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
										<FormField
											control={form.control}
											name="workSchedule"
											render={({ field }) => (
												<FormItem>
													<FormLabel className="text-sm font-medium text-muted-foreground">
														Schedule Type
													</FormLabel>
													<Select
														onValueChange={field.onChange}
														defaultValue={field.value}
													>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder="Select schedule type" />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															{workScheduleOptions.map((option) => (
																<SelectItem
																	key={option.value}
																	value={option.value}
																>
																	{option.label}
																</SelectItem>
															))}
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>

										{watchWorkSchedule === 'generic' && (
											<>
												<FormField
													control={form.control}
													name="workingDays"
													render={({ field }) => (
														<FormItem>
															<FormLabel className="text-sm font-medium text-muted-foreground">
																Working Days
															</FormLabel>
															<Select
																onValueChange={field.onChange}
																defaultValue={field.value}
															>
																<FormControl>
																	<SelectTrigger>
																		<SelectValue placeholder="Select working days" />
																	</SelectTrigger>
																</FormControl>
																<SelectContent>
																	{workingDaysOptions.map((option) => (
																		<SelectItem
																			key={option.value}
																			value={option.value}
																		>
																			{option.label}
																		</SelectItem>
																	))}
																</SelectContent>
															</Select>
															<FormMessage />
														</FormItem>
													)}
												/>

												<FormField
													control={form.control}
													name="workingHours"
													render={({ field }) => (
														<FormItem>
															<FormLabel className="text-sm font-medium text-muted-foreground">
																Working Hours
															</FormLabel>
															<Select
																onValueChange={field.onChange}
																defaultValue={field.value}
															>
																<FormControl>
																	<SelectTrigger>
																		<SelectValue placeholder="Select working hours" />
																	</SelectTrigger>
																</FormControl>
																<SelectContent>
																	{workingHoursOptions.map((option) => (
																		<SelectItem
																			key={option.value}
																			value={option.value}
																		>
																			{option.label}
																		</SelectItem>
																	))}
																</SelectContent>
															</Select>
															<FormMessage />
														</FormItem>
													)}
												/>

												<FormField
													control={form.control}
													name="firstOffDay"
													render={({ field }) => (
														<FormItem>
															<FormLabel className="text-sm font-medium text-muted-foreground">
																First Off Day
															</FormLabel>
															<Select
																onValueChange={field.onChange}
																defaultValue={field.value}
															>
																<FormControl>
																	<SelectTrigger>
																		<SelectValue placeholder="Select first off day" />
																	</SelectTrigger>
																</FormControl>
																<SelectContent>
																	{weekDayOptions.map((option) => (
																		<SelectItem
																			key={option.value}
																			value={option.value}
																		>
																			{option.label}
																		</SelectItem>
																	))}
																</SelectContent>
															</Select>
															<FormMessage />
														</FormItem>
													)}
												/>

												{watchWorkingDays !== '6_DAYS' && (
													<FormField
														control={form.control}
														name="secondOffDay"
														render={({ field }) => (
															<FormItem>
																<FormLabel className="text-sm font-medium text-muted-foreground">
																	Second Off Day
																</FormLabel>
																<Select
																	onValueChange={field.onChange}
																	defaultValue={field.value}
																>
																	<FormControl>
																		<SelectTrigger>
																			<SelectValue placeholder="Select second off day" />
																		</SelectTrigger>
																	</FormControl>
																	<SelectContent>
																		{weekDayOptions.map((option) => (
																			<SelectItem
																				key={option.value}
																				value={option.value}
																			>
																				{option.label}
																			</SelectItem>
																		))}
																	</SelectContent>
																</Select>
																<FormMessage />
															</FormItem>
														)}
													/>
												)}

												{watchWorkingDays === '5.5_DAYS' && (
													<FormField
														control={form.control}
														name="halfDay"
														render={({ field }) => (
															<FormItem>
																<FormLabel className="text-sm font-medium text-muted-foreground">
																	Half Day
																</FormLabel>
																<Select
																	onValueChange={field.onChange}
																	defaultValue={field.value}
																>
																	<FormControl>
																		<SelectTrigger>
																			<SelectValue placeholder="Select half day option" />
																		</SelectTrigger>
																	</FormControl>
																	<SelectContent>
																		{halfDayOptions.map((option) => (
																			<SelectItem
																				key={option.value}
																				value={option.value}
																			>
																				{option.label}
																			</SelectItem>
																		))}
																	</SelectContent>
																</Select>
																<FormMessage />
															</FormItem>
														)}
													/>
												)}
											</>
										)}
									</div>
								</Form>
							) : (
								<div className="grid grid-cols-2 gap-4">
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Schedule Type
										</p>
										<p className="capitalize">
											{employmentDetails.workSchedule}
										</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Working Days
										</p>
										<p>{formatWorkingDays(employmentDetails.workingDays)}</p>
									</div>
									<div>
										<p className="text-sm font-medium text-muted-foreground">
											Working Hours
										</p>
										<p>{employmentDetails.workingHours} hours per day</p>
									</div>
									{employmentDetails.halfDay && (
										<div>
											<p className="text-sm font-medium text-muted-foreground">
												Half Day
											</p>
											<p>{formatHalfDay(employmentDetails.halfDay)}</p>
										</div>
									)}
									{employmentDetails.firstOffDay && (
										<div>
											<p className="text-sm font-medium text-muted-foreground">
												First Off Day
											</p>
											<p className="capitalize">
												{formatOffDay(employmentDetails.firstOffDay)}
											</p>
										</div>
									)}
									{employmentDetails.secondOffDay && (
										<div>
											<p className="text-sm font-medium text-muted-foreground">
												Second Off Day
											</p>
											<p className="capitalize">
												{formatOffDay(employmentDetails.secondOffDay)}
											</p>
										</div>
									)}
								</div>
							)}
						</CardContent>
					</Card>

					{/* Reporting Manager Card */}
					<Card className="md:col-span-2">
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle>Reporting Manager</CardTitle>
						</CardHeader>
						<CardContent>
							{employmentDetails.reportingTo ? (
								<div className="flex items-start gap-4">
									<Avatar className="h-12 w-12">
										<div className="flex h-full w-full items-center justify-center bg-muted text-sm font-semibold uppercase">
											{employmentDetails.reportingTo.nameOnNRIC?.charAt(0) ||
												'M'}
										</div>
									</Avatar>
									<div>
										<h4 className="font-medium">
											{employmentDetails.reportingTo.nameOnNRIC}
										</h4>
										<p className="text-sm text-muted-foreground">
											Employee ID: {employmentDetails.reportingTo.employeeOrgId}
										</p>
										<div className="flex items-center gap-4 mt-1">
											<p className="text-sm">
												<Mail className="h-4 w-4 inline-block mr-1" />
												{employee.email}
											</p>
											<p className="text-sm">
												<Phone className="h-4 w-4 inline-block mr-1" />
												{employmentDetails.reportingTo.countryDialCode}{' '}
												{employmentDetails.reportingTo.mobile}
											</p>
										</div>
									</div>
								</div>
							) : (
								<div className="flex items-center justify-center p-6 text-muted-foreground">
									<p>No reporting manager information available</p>
								</div>
							)}
						</CardContent>
					</Card>
				</div>
			) : (
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle>Employment Details</CardTitle>
					</CardHeader>
					<CardContent className="flex flex-col items-center justify-center p-6 text-center">
						<Briefcase className="h-12 w-12 text-muted-foreground mb-4" />
						<h3 className="text-lg font-medium mb-2">
							Employment Details Coming Soon
						</h3>
						<p className="text-muted-foreground max-w-md">
							This section will display employment details including position,
							department, reporting manager, and other employment-related
							information.
						</p>
					</CardContent>
				</Card>
			)}
		</>
	);
};

export default EmployeeDetailsEmploymentForm;
