'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
	MoreHorizontal,
	Eye,
	Check,
	X,
	MessageSquare,
} from 'lucide-react';

export const EditRequestRowActions = ({
	request,
	onCompareData,
	onApproveRequest,
	onRejectRequest,
}) => {
	const [showRejectDialog, setShowRejectDialog] = useState(false);
	const [rejectionReason, setRejectionReason] = useState('');

	const handleApprove = () => {
		onApproveRequest(request._id);
	};

	const handleReject = () => {
		if (rejectionReason.trim()) {
			onRejectRequest(request._id, rejectionReason);
			setShowRejectDialog(false);
			setRejectionReason('');
		}
	};

	const handleCompare = () => {
		onCompareData(request);
	};

	const isPending = request.status === 'pending';

	return (
		<>
			<div className="flex items-center gap-2">
				{/* Quick Actions for Pending Requests */}
				{isPending && (
					<>
						<Button
							variant="outline"
							size="sm"
							onClick={handleApprove}
							className="h-8 px-2 text-green-600 hover:text-green-700 hover:bg-green-50"
						>
							<Check className="h-3 w-3" />
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={() => setShowRejectDialog(true)}
							className="h-8 px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
						>
							<X className="h-3 w-3" />
						</Button>
					</>
				)}

				{/* Compare Button */}
				<Button
					variant="outline"
					size="sm"
					onClick={handleCompare}
					className="h-8 px-2"
				>
					<Eye className="h-3 w-3" />
				</Button>

				{/* More Actions Dropdown */}
				{/* <DropdownMenu>
					<DropdownMenuTrigger asChild>
						<Button
							variant="ghost"
							className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
						>
							<MoreHorizontal className="h-4 w-4" />
							<span className="sr-only">Open menu</span>
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent align="end" className="w-[160px]">
						<DropdownMenuItem onClick={handleCompare}>
							<Eye className="mr-2 h-4 w-4" />
							Compare Data
						</DropdownMenuItem>
						{isPending && (
							<>
								<DropdownMenuSeparator />
								<DropdownMenuItem onClick={handleApprove}>
									<Check className="mr-2 h-4 w-4 text-green-600" />
									Approve
								</DropdownMenuItem>
								<DropdownMenuItem onClick={() => setShowRejectDialog(true)}>
									<X className="mr-2 h-4 w-4 text-red-600" />
									Reject
								</DropdownMenuItem>
							</>
						)}
						{request.status === 'rejected' && request.rejectionReason && (
							<>
								<DropdownMenuSeparator />
								<DropdownMenuItem>
									<MessageSquare className="mr-2 h-4 w-4" />
									View Reason
								</DropdownMenuItem>
							</>
						)}
					</DropdownMenuContent>
				</DropdownMenu> */}
			</div>

			{/* Reject Dialog */}
			<Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
				<DialogContent className="sm:max-w-[425px]">
					<DialogHeader>
						<DialogTitle>Reject Request</DialogTitle>
						<DialogDescription>
							Please provide a reason for rejecting this update request.
						</DialogDescription>
					</DialogHeader>
					<div className="grid gap-4 py-4">
						<div className="grid gap-2">
							<Label htmlFor="reason">Rejection Reason</Label>
							<Textarea
								id="reason"
								placeholder="Enter the reason for rejection..."
								value={rejectionReason}
								onChange={(e) => setRejectionReason(e.target.value)}
								className="min-h-[100px]"
							/>
						</div>
					</div>
					<DialogFooter>
						<Button
							type="button"
							variant="outline"
							onClick={() => {
								setShowRejectDialog(false);
								setRejectionReason('');
							}}
						>
							Cancel
						</Button>
						<Button
							type="button"
							variant="destructive"
							onClick={handleReject}
							disabled={!rejectionReason.trim()}
						>
							Reject Request
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</>
	);
};
