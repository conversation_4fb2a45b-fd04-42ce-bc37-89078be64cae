import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { MultipleEntriesManager } from '../MultipleEntriesManager';

export function ExperienceFields({ form }) {
	const experience = form.watch('experience') || [];

	const handleExperienceChange = (newExperience) => {
		form.setValue('experience', newExperience);
	};

	return (
		<>
			<MultipleEntriesManager
				section="experience"
				entries={experience}
				onEntriesChange={handleExperienceChange}
			/>

			<FormField
				control={form.control}
				name="reason"
				render={({ field }) => (
					<FormItem>
						<FormLabel>Reason for Change *</FormLabel>
						<FormControl>
							<Textarea
								placeholder="Please provide a reason for these changes..."
								className="min-h-[100px]"
								{...field}
							/>
						</FormControl>
						<FormMessage />
					</FormItem>
				)}
			/>
		</>
	);
}
