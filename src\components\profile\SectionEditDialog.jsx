import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { getEditRequestSchema } from '@/lib/validations/editRequestValidation';
import { convertIsoToDateInput, convertDateInputToIso } from './utils/dateUtils';
import { addCalculatedAges } from '@/lib/utils/editRequestUtils';
import { PersonalFields } from './fields/PersonalFields';
import { FamilyFields } from './fields/FamilyFields';
import { ContactFields } from './fields/ContactFields';
import { EducationFields } from './fields/EducationFields';
import { ExperienceFields } from './fields/ExperienceFields';

// Helper function to capitalize strings
const capitalize = (str) => {
	if (!str) return '';
	return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

export function SectionEditDialog({ section, currentData, onSubmit, onClose }) {
	// Get the appropriate schema for the section
	const schema = getEditRequestSchema(section);

	// Helper function to get default values based on section and current data
	const getDefaultValues = (section, currentData) => {
		if (!currentData) return { reason: '' };

		if (section === 'family') {
			// For family section, handle children with age calculation and date conversion
			return {
				reason: '',
				...currentData,
				children: addCalculatedAges(currentData.children || []).map(child => ({
					...child,
					dob: convertIsoToDateInput(child.dob) // Convert ISO to YYYY-MM-DD for form display
				})),
			};
		} else {
			// For personal section
			return {
				reason: '',
				...currentData,
				dob: convertIsoToDateInput(currentData.dob),
			};
		}
	};

	// Initialize form with react-hook-form and Zod validation
	const form = useForm({
		resolver: zodResolver(schema),
		defaultValues: getDefaultValues(section, currentData),
	});

	// Reset form when section or currentData changes
	useEffect(() => {
		if (section && currentData) {
			form.reset(getDefaultValues(section, currentData));
		}
	}, [section, currentData, form]);

	// Handle form submission
	const handleFormSubmit = async (data) => {
		const { reason, ...formData } = data;

		// Convert DOB from YYYY-MM-DD to ISO format for payload
		if (formData.dob) {
			formData.dob = convertDateInputToIso(formData.dob);
		}

		// Convert children DOB from YYYY-MM-DD to ISO format for payload
		if (formData.children && Array.isArray(formData.children)) {
			formData.children = formData.children.map((child) => ({
				...child,
				dob: child.dob ? convertDateInputToIso(child.dob) : child.dob,
			}));
		}

		// Submit the request and wait for result
		const success = await onSubmit(section, currentData, formData, reason);

		// Only close dialog and reset form if submission was successful
		if (success) {
			onClose();
			form.reset();
		}
	};

	const renderFields = () => {
		switch (section) {
			case 'personal':
				return <PersonalFields form={form} />;
			case 'family':
				return <FamilyFields form={form} />;
			case 'education':
				return <EducationFields form={form} />;
			case 'experience':
				return <ExperienceFields form={form} />;
			case 'contact':
				return <ContactFields form={form} />;
			default:
				return <div>Unknown section</div>;
		}
	};

	if (!section) return null;

	return (
		<Dialog open={!!section} onOpenChange={() => onClose()}>
			<DialogContent className="max-w-4xl w-[95vw] max-h-[90vh] overflow-hidden flex flex-col">
				<DialogHeader className="flex-shrink-0 pb-4 border-b">
					<DialogTitle className="text-xl font-semibold">
						Edit {capitalize(section)} Information
					</DialogTitle>
					<DialogDescription className="text-sm text-muted-foreground">
						Request changes to your {section} information. All changes require
						approval from your manager.
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(handleFormSubmit)}
						className="flex-1 overflow-y-auto py-4"
					>
						<div className="space-y-6">
							<Alert className="border-amber-200 bg-amber-50">
								<AlertCircle className="h-4 w-4 text-amber-600" />
								<AlertTitle className="text-amber-800">
									Approval Required
								</AlertTitle>
								<AlertDescription className="text-amber-700">
									Changes will be reviewed by your reporting manager before
									being updated in your profile.
								</AlertDescription>
							</Alert>

							{renderFields()}
						</div>

						<DialogFooter className="flex-shrink-0 pt-6 border-t mt-6">
							<Button type="button" variant="outline" onClick={onClose}>
								Cancel
							</Button>
							<Button type="submit">Submit Request</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}
