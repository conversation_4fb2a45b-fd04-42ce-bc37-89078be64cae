import { configureStore } from '@reduxjs/toolkit';
import authReducer from './features/auth/authSlice';
import clientAdminReducer from './features/client-admin/clientAdminSlice';
import employeesReducer from './features/employees/employeeSlice';
import updateEmployeeReducer from './features/employees/updateEmployeeSlice';
import editRequestReducer from './features/employees/editRequestSlice';
import businessUnitReducer from './features/company-infrastructure/businessUnitSlice';
import departmentReducer from './features/company-infrastructure/departmentSlice';
import designationReducer from './features/company-infrastructure/designationSlice';
import holidayReducer from './features/holiday/holidaySlice';
import holidayGroupReducer from './features/holiday/holidayGroupSlice';
import locationReducer from './features/location/locationSlice';
import companyDetailsReducer from './features/company-details/companyDetailsSlice';
import glorifiedClientAdminReducer from './features/glorified-client-admin/glorifiedClientAdminSlice';
import hrModuleReducer from './features/hr-module/hrModuleSlice';
import moduleAdminsReducer from './features/module-admin/moduleAdminSlice';
import superAdminReducer from './features/super-admin/superAdminSlice';
import chatReducer from './features/chat/chatSlice';
import attendanceReducer from './features/attendance/attendanceSlice';

export const makeStore = () => {
	return configureStore({
		reducer: {
			auth: authReducer,
			clientAdmin: clientAdminReducer,
			employee: employeesReducer,
			updateEmployee: updateEmployeeReducer,
      editRequest: editRequestReducer,
			businessUnit: businessUnitReducer,
			department: departmentReducer,
			designation: designationReducer,
			holiday: holidayReducer,
			holidayGroup: holidayGroupReducer,
			location: locationReducer,
			companyDetails: companyDetailsReducer,
			glorifiedClientAdmin: glorifiedClientAdminReducer,
			hrModule: hrModuleReducer,
			moduleAdmin: moduleAdminsReducer,
			superAdmin: superAdminReducer,
			chat: chatReducer,
			attendance: attendanceReducer,
		},
	});
};
