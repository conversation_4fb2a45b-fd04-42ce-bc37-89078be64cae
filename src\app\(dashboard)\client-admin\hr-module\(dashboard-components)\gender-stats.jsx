import {
	User,
	UserRound,
	UserIcon as Male,
	UserIcon as Female,
	GroupIcon as Others,
	Users,
} from 'lucide-react';
import {
	Card,
	CardHeader,
	CardTitle,
	CardDescription,
	CardContent,
	CardFooter,
} from '@/components/ui/card';

export function GenderDistributionAlt({
	maleCount = 0,
	femaleCount = 0,
	othersCount = 0,
}) {
	// Add default values of 0 to prevent undefined errors
	const maleSafe = typeof maleCount === 'number' ? maleCount : 0;
	const femaleSafe = typeof femaleCount === 'number' ? femaleCount : 0;
	const othersSafe = typeof othersCount === 'number' ? othersCount : 0;

	const totalCount = maleSafe + femaleSafe + othersSafe;

	// Avoid division by zero
	const malePercentage =
		totalCount > 0 ? Math.round((maleSafe / totalCount) * 100) : 0;
	const femalePercentage =
		totalCount > 0 ? Math.round((femaleSafe / totalCount) * 100) : 0;
	const othersPercentage =
		totalCount > 0 ? 100 - malePercentage - femalePercentage : 0;

	// Helper function to safely format numbers
	const formatNumber = (num) => {
		return typeof num === 'number' ? num.toLocaleString() : '0';
	};

	return (
		<Card className="flex flex-col">
			<CardHeader className="pb-0">
				<CardTitle>Genders</CardTitle>
				<CardDescription>Gender Distribution</CardDescription>
			</CardHeader>

			<CardContent className="flex flex-1 flex-col items-center justify-center gap-6 py-6">
				{/* Total count */}
				<div className="text-center">
					<div className="text-3xl font-bold">{formatNumber(totalCount)}</div>
					<div className="text-sm text-muted-foreground">Total Employees</div>
				</div>

				{/* Gender breakdown */}
				<div className="grid grid-cols-3 w-full gap-2">
					{/* Male stats */}
					<div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-blue-50">
						<User className="size-8 text-blue-600" />
						<div className="text-lg font-bold">{formatNumber(maleSafe)}</div>
						<div className="text-xs text-muted-foreground">
							Males ({malePercentage}%)
						</div>
					</div>

					{/* Female stats */}
					<div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-pink-50">
						<UserRound className="size-8 text-pink-600" />
						<div className="text-lg font-bold">{formatNumber(femaleSafe)}</div>
						<div className="text-xs text-muted-foreground">
							Females ({femalePercentage}%)
						</div>
					</div>

					{/* Others stats */}
					<div className="flex flex-col items-center gap-2 p-3 rounded-lg bg-purple-50">
						<Users className="size-8 text-purple-600" />
						<div className="text-lg font-bold">{formatNumber(othersSafe)}</div>
						<div className="text-xs text-muted-foreground">
							Others ({othersPercentage}%)
						</div>
					</div>
				</div>
			</CardContent>

			<CardFooter className="flex-col gap-2 text-sm border-t pt-4">
				<div className="leading-none text-muted-foreground">
					Showing complete gender distribution of employees
				</div>
			</CardFooter>
		</Card>
	);
}

export function GenderDistributionCard({
	maleCount = 0,
	femaleCount = 0,
	othersCount = 0,
}) {
	// Add default values of 0 to prevent undefined errors
	const maleSafe = typeof maleCount === 'number' ? maleCount : 0;
	const femaleSafe = typeof femaleCount === 'number' ? femaleCount : 0;
	const othersSafe = typeof othersCount === 'number' ? othersCount : 0;

	const totalCount = maleSafe + femaleSafe + othersSafe;

	// Helper function to safely format numbers
	const formatNumber = (num) => {
		return typeof num === 'number' ? num.toLocaleString() : '0';
	};

	return (
		<Card className="flex flex-col">
			<CardHeader className="pb-0">
				<CardTitle>Genders</CardTitle>
				<CardDescription>Gender Distribution</CardDescription>
			</CardHeader>

			<CardContent className="flex flex-1 items-center justify-center py-6">
				<div className="flex w-full justify-around items-center">
					{/* Male stats */}
					<div className="flex flex-col items-center gap-3">
						<div className="rounded-full bg-blue-100 p-4">
							<Male className="size-10 text-blue-600" />
						</div>
						<div className="text-center">
							<div className="text-xl font-bold">{formatNumber(maleSafe)}</div>
							<div className="text-sm text-muted-foreground">Males</div>
						</div>
					</div>

					{/* Female stats */}
					<div className="flex flex-col items-center gap-3">
						<div className="rounded-full bg-pink-100 p-4">
							<Female className="size-10 text-pink-600" />
						</div>
						<div className="text-center">
							<div className="text-xl font-bold">
								{formatNumber(femaleSafe)}
							</div>
							<div className="text-sm text-muted-foreground">Females</div>
						</div>
					</div>

					{/* Others stats */}
					<div className="flex flex-col items-center gap-3">
						<div className="rounded-full bg-purple-100 p-4">
							<Others className="size-10 text-purple-600" />
						</div>
						<div className="text-center">
							<div className="text-xl font-bold">
								{formatNumber(othersSafe)}
							</div>
							<div className="text-sm text-muted-foreground">Others</div>
						</div>
					</div>
				</div>
			</CardContent>

			<CardFooter className="flex-col gap-2 text-sm border-t pt-4">
				<div className="leading-none text-muted-foreground">
					Total: {formatNumber(totalCount)} employees
				</div>
			</CardFooter>
		</Card>
	);
}

// Example usage
export function ExampleUsage() {
	// Sample data
	const maleCount = 1234;
	const femaleCount = 987;

	return (
		<div className="w-full max-w-md mx-auto">
			<GenderDistributionCard maleCount={maleCount} femaleCount={femaleCount} />
		</div>
	);
}
