'use client';

import { useState, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	fetchEditRequests,
	addEditRequest,
	deleteEditRequests,
	updateEditRequest,
} from '@/lib/features/employees/editRequestSlice';
import { findPendingRequest } from '@/lib/utils/editRequestUtils';

// Import modular components
import { ProfileHeader } from './ProfileHeader';
import { NotificationSystem } from './NotificationSystem';
import { PendingRequests } from './PendingRequests';
import { CancelRequestDialog } from './CancelRequestDialog';
import { ProfileTabs } from './ProfileTabs';
import { SectionEditDialog } from './SectionEditDialog';
import { AddChildDialog } from './AddChildDialog';

export function ProfilePageMain() {
	// Redux state
	const dispatch = useAppDispatch();
	const { userProfile, isLoading } = useAppSelector((state) => state.employee);
	const { editRequests } = useAppSelector((state) => state.editRequest);
	const { authenticatedUser } = useAppSelector((state) => state.auth);

	// Local state
	const [notifications, setNotifications] = useState([]);
	const [cancelRequestId, setCancelRequestId] = useState(null);
	const [editingSection, setEditingSection] = useState(null);
	const [showAddChildDialog, setShowAddChildDialog] = useState(false);

	// Extract data from userProfile
	const data = userProfile || {};
	const {
		personalDetails = {},
		education = [],
		family = {},
		experience = [],
		contact = [],
	} = data;

	// Get pending edit requests
	const pendingChanges = editRequests || [];

	// Fetch edit requests on component mount
	useEffect(() => {
		if (authenticatedUser?.userId) {
			dispatch(fetchEditRequests({}));
		}
	}, [dispatch, authenticatedUser?.userId]);

	// Notification system
	const addNotification = (message, type = 'success') => {
		const id = Date.now();
		setNotifications((prev) => [...prev, { id, message, type }]);
		setTimeout(() => {
			setNotifications((prev) =>
				prev.filter((notification) => notification.id !== id)
			);
		}, 5000);
	};

	// Handle section edit requests
	const handleSectionEditRequest = (section, oldData, newData, reason) => {
		// Check for existing pending request
		const pendingRequest = findPendingRequest(
			editRequests,
			authenticatedUser?.userId,
			`${section.toLowerCase().replace(' ', '-')}-details`
		);

		if (pendingRequest) {
			// Update existing pending request
			const updatedRequestData = {
				id: pendingRequest._id,
				oldData,
				newData,
				reason,
			};

			dispatch(updateEditRequest(updatedRequestData));
			addNotification(`Your pending request for ${section} has been updated.`);
		} else {
			// Create new request
			const editRequestData = {
				userId: authenticatedUser?.userId,
				section: `${section.toLowerCase().replace(' ', '-')}-details`,
				oldData,
				newData,
				reason,
			};

			dispatch(addEditRequest(editRequestData));
			addNotification(
				`Your request to update ${section} has been submitted for review.`
			);
		}
	};

	// Handle add child request
	const handleAddChildRequest = (childData, reason) => {
		// Get current family data
		const currentFamilyData = getSectionData('family');

		// Add the new child to the children array with calculated age
		const updatedChildren = [
			...(currentFamilyData.children || []),
			{
				...childData,
				age: Math.floor(
					(new Date() - new Date(childData.dob)) /
						(365.25 * 24 * 60 * 60 * 1000)
				),
			},
		];

		// Create family edit request with the new child
		const newFamilyData = {
			...currentFamilyData,
			children: updatedChildren,
		};

		// Use the existing section edit request handler
		handleSectionEditRequest('family', currentFamilyData, newFamilyData, reason);
		setShowAddChildDialog(false);
	};

	// Handle cancel request
	const handleCancelRequest = (id) => {
		dispatch(deleteEditRequests([id]));
		setCancelRequestId(null);
		addNotification('Your request has been cancelled.', 'success');
	};

	// Helper function to get section data
	const getSectionData = (section) => {
		switch (section) {
			case 'personal':
				return personalDetails;
			case 'family':
				return family;
			case 'education':
				return education;
			case 'experience':
				return experience;
			case 'contact':
				return contact;
			default:
				return {};
		}
	};

	// Loading state
	if (isLoading) {
		return (
			<div className="container mx-auto p-4 flex items-center justify-center min-h-[400px]">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto mb-4"></div>
					<p className="text-muted-foreground">Loading profile...</p>
				</div>
			</div>
		);
	}

	// No profile data
	if (!userProfile) {
		return (
			<div className="container mx-auto p-4 flex items-center justify-center min-h-[400px]">
				<div className="text-center">
					<p className="text-muted-foreground">No profile data available.</p>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto p-4">
			{/* Profile Header */}
			<ProfileHeader 
				personalDetails={personalDetails}
				experience={experience}
				data={data}
			/>

			{/* Notification System */}
			<NotificationSystem notifications={notifications} />

			{/* Pending Requests */}
			<PendingRequests 
				pendingChanges={pendingChanges}
				onCancelRequest={setCancelRequestId}
			/>

			{/* Profile Tabs */}
			<ProfileTabs
				personalDetails={personalDetails}
				family={family}
				education={education}
				experience={experience}
				contact={contact}
				onEditSection={setEditingSection}
				onAddChild={() => setShowAddChildDialog(true)}
			/>

			{/* Cancel Request Dialog */}
			<CancelRequestDialog
				cancelRequestId={cancelRequestId}
				pendingChanges={pendingChanges}
				onCancel={() => setCancelRequestId(null)}
				onConfirm={handleCancelRequest}
			/>

			{/* Section Edit Dialog */}
			<SectionEditDialog
				section={editingSection}
				currentData={getSectionData(editingSection)}
				onSubmit={handleSectionEditRequest}
				onClose={() => setEditingSection(null)}
			/>

			{/* Add Child Dialog */}
			<AddChildDialog
				open={showAddChildDialog}
				onSubmit={handleAddChildRequest}
				onClose={() => setShowAddChildDialog(false)}
			/>
		</div>
	);
}
