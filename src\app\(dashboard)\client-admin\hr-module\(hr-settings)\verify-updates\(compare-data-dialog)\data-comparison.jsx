'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ArrowRight, Minus, Plus } from 'lucide-react';
import { format } from 'date-fns';

const DataComparison = ({ oldData, newData, section }) => {
	// Helper function to render a single field comparison
	const renderFieldComparison = (key, oldValue, newValue, label) => {
		const hasChanged = JSON.stringify(oldValue) !== JSON.stringify(newValue);
		
		if (!hasChanged && (oldValue === undefined || oldValue === null || oldValue === '')) {
			return null;
		}

		return (
			<div key={key} className="grid grid-cols-1 md:grid-cols-3 gap-4 py-3">
				<div className="font-medium text-sm text-muted-foreground">
					{label || key.charAt(0).toUpperCase() + key.slice(1)}
				</div>
				<div className="space-y-2">
					<div className="text-xs text-muted-foreground">Current</div>
					<div className={`p-2 rounded border ${hasChanged ? 'bg-red-50 border-red-200' : 'bg-gray-50'}`}>
						{renderValue(oldValue)}
					</div>
				</div>
				<div className="space-y-2">
					<div className="text-xs text-muted-foreground">Proposed</div>
					<div className={`p-2 rounded border ${hasChanged ? 'bg-green-50 border-green-200' : 'bg-gray-50'}`}>
						{renderValue(newValue)}
					</div>
				</div>
			</div>
		);
	};

	// Helper function to render values
	const renderValue = (value) => {
		if (value === null || value === undefined || value === '') {
			return <span className="text-muted-foreground italic">Not set</span>;
		}
		
		if (typeof value === 'boolean') {
			return value ? 'Yes' : 'No';
		}
		
		if (typeof value === 'object') {
			return <pre className="text-xs">{JSON.stringify(value, null, 2)}</pre>;
		}
		
		// Check if it's a date string
		if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}/.test(value)) {
			try {
				return format(new Date(value), 'MMM dd, yyyy');
			} catch {
				return value;
			}
		}
		
		return String(value);
	};

	// Helper function to render array comparison (for education, experience, etc.)
	const renderArrayComparison = (oldArray, newArray, itemLabel) => {
		const maxLength = Math.max(oldArray?.length || 0, newArray?.length || 0);
		
		return (
			<div className="space-y-4">
				{Array.from({ length: maxLength }, (_, index) => {
					const oldItem = oldArray?.[index];
					const newItem = newArray?.[index];
					
					if (!oldItem && !newItem) return null;
					
					return (
						<Card key={index} className="border-l-4 border-l-blue-500">
							<CardHeader className="pb-2">
								<CardTitle className="text-sm">
									{itemLabel} {index + 1}
									{!oldItem && <Badge variant="secondary" className="ml-2">New</Badge>}
									{!newItem && <Badge variant="destructive" className="ml-2">Removed</Badge>}
								</CardTitle>
							</CardHeader>
							<CardContent className="space-y-2">
								{oldItem && newItem ? (
									// Compare existing items
									Object.keys({ ...oldItem, ...newItem }).map(key => 
										renderFieldComparison(`${index}-${key}`, oldItem[key], newItem[key], key)
									)
								) : oldItem ? (
									// Show removed item
									<div className="p-3 bg-red-50 border border-red-200 rounded">
										<div className="text-sm font-medium text-red-800 mb-2">Removed Item</div>
										<pre className="text-xs text-red-700">{JSON.stringify(oldItem, null, 2)}</pre>
									</div>
								) : (
									// Show new item
									<div className="p-3 bg-green-50 border border-green-200 rounded">
										<div className="text-sm font-medium text-green-800 mb-2">New Item</div>
										<pre className="text-xs text-green-700">{JSON.stringify(newItem, null, 2)}</pre>
									</div>
								)}
							</CardContent>
						</Card>
					);
				})}
			</div>
		);
	};

	// Section-specific rendering
	const renderSectionComparison = () => {
		switch (section) {
			case 'personal-details':
				return (
					<div className="space-y-1">
						{renderFieldComparison('nameOnNRIC', oldData.nameOnNRIC, newData.nameOnNRIC, 'Full Name')}
						{renderFieldComparison('dateOfBirth', oldData.dateOfBirth, newData.dateOfBirth, 'Date of Birth')}
						{renderFieldComparison('gender', oldData.gender, newData.gender, 'Gender')}
						{renderFieldComparison('maritalStatus', oldData.maritalStatus, newData.maritalStatus, 'Marital Status')}
						{renderFieldComparison('nationality', oldData.nationality, newData.nationality, 'Nationality')}
						{renderFieldComparison('religion', oldData.religion, newData.religion, 'Religion')}
					</div>
				);

			case 'contact-details':
				return (
					<div className="space-y-1">
						{renderFieldComparison('mobile', oldData.mobile, newData.mobile, 'Mobile Number')}
						{renderFieldComparison('email', oldData.email, newData.email, 'Email Address')}
						{renderFieldComparison('address', oldData.address, newData.address, 'Address')}
						{renderFieldComparison('emergencyContact', oldData.emergencyContact, newData.emergencyContact, 'Emergency Contact')}
					</div>
				);

			case 'education-details':
				return (
					<div className="space-y-4">
						{oldData.education || newData.education ? (
							renderArrayComparison(oldData.education, newData.education, 'Education')
						) : (
							<div className="text-center text-muted-foreground py-8">
								No education data to compare
							</div>
						)}
					</div>
				);

			case 'employment-details':
				return (
					<div className="space-y-1">
						{renderFieldComparison('businessUnit', oldData.businessUnit, newData.businessUnit, 'Business Unit')}
						{renderFieldComparison('department', oldData.department, newData.department, 'Department')}
						{renderFieldComparison('designation', oldData.designation, newData.designation, 'Designation')}
						{renderFieldComparison('employmentType', oldData.employmentType, newData.employmentType, 'Employment Type')}
						{renderFieldComparison('workSchedule', oldData.workSchedule, newData.workSchedule, 'Work Schedule')}
					</div>
				);

			default:
				// Generic comparison for other sections
				const allKeys = new Set([...Object.keys(oldData || {}), ...Object.keys(newData || {})]);
				return (
					<div className="space-y-1">
						{Array.from(allKeys).map(key => 
							renderFieldComparison(key, oldData?.[key], newData?.[key])
						)}
					</div>
				);
		}
	};

	return (
		<div className="space-y-4">
			<div className="flex items-center gap-2">
				<h3 className="text-lg font-semibold">Data Changes</h3>
				<Badge variant="outline">
					{section.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
				</Badge>
			</div>
			
			<div className="border rounded-lg p-4">
				{renderSectionComparison()}
			</div>
		</div>
	);
};

export default DataComparison;
