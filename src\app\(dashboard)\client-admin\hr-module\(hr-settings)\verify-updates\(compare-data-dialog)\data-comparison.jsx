'use client';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { ArrowRight, Minus, Plus } from 'lucide-react';
import { format } from 'date-fns';

const DataComparison = ({ oldData, newData, section }) => {
	// Helper function to render a single field comparison
	// Now shows all fields from newData and compares with oldData
	const renderFieldComparison = (key, oldValue, newValue, label) => {
		const hasChanged = JSON.stringify(oldValue) !== JSON.stringify(newValue);

		return (
			<div key={key} className="grid grid-cols-1 md:grid-cols-3 gap-4 py-3">
				<div className="font-medium text-sm text-muted-foreground">
					{label || key.charAt(0).toUpperCase() + key.slice(1)}
				</div>
				<div className="space-y-2">
					<div className="text-xs text-muted-foreground">Current</div>
					<div className={`p-2 rounded border ${hasChanged ? 'bg-red-50 border-red-200' : 'bg-gray-50'}`}>
						{renderValue(oldValue)}
					</div>
				</div>
				<div className="space-y-2">
					<div className="text-xs text-muted-foreground">Proposed</div>
					<div className={`p-2 rounded border ${hasChanged ? 'bg-green-50 border-green-200' : 'bg-gray-50'}`}>
						{renderValue(newValue)}
					</div>
				</div>
			</div>
		);
	};

	// Helper function to render values
	const renderValue = (value) => {
		if (value === null || value === undefined || value === '') {
			return <span className="text-muted-foreground italic">Not set</span>;
		}

		if (typeof value === 'boolean') {
			return value ? 'Yes' : 'No';
		}

		if (typeof value === 'object') {
			return <pre className="text-xs">{JSON.stringify(value, null, 2)}</pre>;
		}

		// Check if it's a date string
		if (typeof value === 'string' && /^\d{4}-\d{2}-\d{2}/.test(value)) {
			try {
				return format(new Date(value), 'MMM dd, yyyy');
			} catch {
				return value;
			}
		}

		return String(value);
	};

	// Helper function to render array comparison (for education, experience, etc.)
	// Now primarily based on newArray structure, showing comparison with oldArray
	const renderArrayComparison = (oldArray, newArray, itemLabel) => {
		// If no newArray, show that no data is being requested
		if (!newArray || newArray.length === 0) {
			return (
				<div className="text-center text-muted-foreground py-8">
					No {itemLabel.toLowerCase()} data in the request
				</div>
			);
		}

		return (
			<div className="space-y-4">
				{newArray.map((newItem, index) => {
					// Find corresponding old item (could be at different index)
					const oldItem = oldArray?.[index];
					const isNewItem = !oldItem;

					return (
						<Card key={index} className="border-l-4 border-l-blue-500">
							<CardHeader className="pb-2">
								<CardTitle className="text-sm">
									{itemLabel} {index + 1}
									{isNewItem && <Badge variant="secondary" className="ml-2">New</Badge>}
								</CardTitle>
							</CardHeader>
							<CardContent className="space-y-2">
								{/* Show all fields from newItem and compare with oldItem */}
								{Object.keys(newItem).map(key =>
									renderFieldComparison(
										`${index}-${key}`,
										oldItem?.[key],
										newItem[key],
										key.charAt(0).toUpperCase() + key.slice(1)
									)
								)}
							</CardContent>
						</Card>
					);
				})}
			</div>
		);
	};

	// Section-specific rendering
	// Now based on newData structure, comparing with oldData
	const renderSectionComparison = () => {
		// If no newData, show that no changes are being requested
		if (!newData || Object.keys(newData).length === 0) {
			return (
				<div className="text-center text-muted-foreground py-8">
					No data changes in this request
				</div>
			);
		}

		switch (section) {
			case 'personal-details':
			case 'personal':
				return (
					<div className="space-y-1">
						{/* Show all fields from newData */}
						{Object.keys(newData).map(key => {
							const fieldLabels = {
								nameOnNRIC: 'Full Name',
								dateOfBirth: 'Date of Birth',
								dob: 'Date of Birth',
								gender: 'Gender',
								maritalStatus: 'Marital Status',
								nationality: 'Nationality',
								religion: 'Religion',
								race: 'Race',
								icFinNumber: 'IC/FIN Number',
								mobile: 'Mobile Number',
								countryDialCode: 'Country Code'
							};

							return renderFieldComparison(
								key,
								oldData?.[key],
								newData[key],
								fieldLabels[key] || key.charAt(0).toUpperCase() + key.slice(1)
							);
						})}
					</div>
				);

			case 'family-details':
			case 'family':
				return (
					<div className="space-y-1">
						{/* Show all fields from newData */}
						{Object.keys(newData).map(key => {
							const fieldLabels = {
								maritalStatus: 'Marital Status',
								spouseName: 'Spouse Name',
								spouseEmploymentStatus: 'Spouse Employment Status',
								children: 'Children'
							};

							// Handle children array separately
							if (key === 'children' && Array.isArray(newData[key])) {
								return (
									<div key={key} className="space-y-4">
										<h4 className="font-medium text-sm text-muted-foreground">Children</h4>
										{renderArrayComparison(oldData?.children, newData.children, 'Child')}
									</div>
								);
							}

							return renderFieldComparison(
								key,
								oldData?.[key],
								newData[key],
								fieldLabels[key] || key.charAt(0).toUpperCase() + key.slice(1)
							);
						})}
					</div>
				);

			case 'contact-details':
			case 'contact':
				return (
					<div className="space-y-4">
						{/* Handle contact array */}
						{newData.contact && Array.isArray(newData.contact) ? (
							renderArrayComparison(oldData?.contact, newData.contact, 'Contact')
						) : (
							/* Handle individual contact fields */
							<div className="space-y-1">
								{Object.keys(newData).map(key => {
									const fieldLabels = {
										mobile: 'Mobile Number',
										email: 'Email Address',
										address: 'Address',
										emergencyContact: 'Emergency Contact',
										countryDialCode: 'Country Code',
										phone: 'Phone Number',
										name: 'Contact Name',
										relationship: 'Relationship',
										type: 'Contact Type'
									};

									return renderFieldComparison(
										key,
										oldData?.[key],
										newData[key],
										fieldLabels[key] || key.charAt(0).toUpperCase() + key.slice(1)
									);
								})}
							</div>
						)}
					</div>
				);

			case 'education-details':
			case 'education':
				return (
					<div className="space-y-4">
						{newData.education && Array.isArray(newData.education) ? (
							renderArrayComparison(oldData?.education, newData.education, 'Education')
						) : (
							<div className="text-center text-muted-foreground py-8">
								No education data in the request
							</div>
						)}
					</div>
				);

			case 'experience-details':
			case 'experience':
				return (
					<div className="space-y-4">
						{newData.experience && Array.isArray(newData.experience) ? (
							renderArrayComparison(oldData?.experience, newData.experience, 'Experience')
						) : (
							<div className="text-center text-muted-foreground py-8">
								No experience data in the request
							</div>
						)}
					</div>
				);

			case 'employment-details':
				return (
					<div className="space-y-1">
						{/* Show all fields from newData */}
						{Object.keys(newData).map(key => {
							const fieldLabels = {
								businessUnit: 'Business Unit',
								department: 'Department',
								designation: 'Designation',
								employmentType: 'Employment Type',
								workSchedule: 'Work Schedule'
							};

							return renderFieldComparison(
								key,
								oldData?.[key],
								newData[key],
								fieldLabels[key] || key.charAt(0).toUpperCase() + key.slice(1)
							);
						})}
					</div>
				);

			default:
				// Generic comparison for other sections - based on newData
				return (
					<div className="space-y-1">
						{Object.keys(newData).map(key => {
							// Handle arrays
							if (Array.isArray(newData[key])) {
								return (
									<div key={key} className="space-y-4">
										<h4 className="font-medium text-sm text-muted-foreground">
											{key.charAt(0).toUpperCase() + key.slice(1)}
										</h4>
										{renderArrayComparison(oldData?.[key], newData[key], key.charAt(0).toUpperCase() + key.slice(1))}
									</div>
								);
							}

							return renderFieldComparison(
								key,
								oldData?.[key],
								newData[key],
								key.charAt(0).toUpperCase() + key.slice(1)
							);
						})}
					</div>
				);
		}
	};

	return (
		<div className="space-y-4">
			<div className="flex items-center gap-2">
				<h3 className="text-lg font-semibold">Data Changes</h3>
				<Badge variant="outline">
					{section.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
				</Badge>
			</div>

			<div className="border rounded-lg p-4">
				{renderSectionComparison()}
			</div>
		</div>
	);
};

export default DataComparison;
