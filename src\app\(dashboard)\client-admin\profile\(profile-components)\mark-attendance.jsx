import React, { useState, useEffect } from 'react';
import { CoffeeIcon, RefreshCwIcon, Laptop } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from '@/components/ui/tooltip';
import { addMinutes, addHours, set } from 'date-fns';
import ConfirmDialog from '@/components/confirm-dialog';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	getAttendanceLogs,
	getStatus,
	clockIn,
	clockOut,
	endBreak,
	startBreak,
} from '@/lib/features/attendance/attendanceSlice';
import { SimpleLoader } from '@/components/loading-component';

const MarkAttendance = () => {
	const dispatch = useAppDispatch();
	const {
		attendanceLogs,
		currentLogStatus,
		isLoading,
		clockInDelay,
		clockOutDelay,
		clockInLimit,
		clockOutLimit,
	} = useAppSelector((state) => state.attendance);

	const [clockedIn, setClockedIn] = useState(
		currentLogStatus?.status === 'clockIn'
	);
	const [canClockIn, setCanClockIn] = useState(false);
	const [canClockOut, setCanClockOut] = useState(false);
	const [isOnBreak, setIsOnBreak] = useState(false);
	const [clockInTooltipMsg, setClockInTooltipMsg] = useState('');
	const [clockOutTooltipMsg, setClockOutTooltipMsg] = useState('');

	const checkClockingConditions = () => {
		if (!attendanceLogs || attendanceLogs.length === 0) {
			setCanClockIn(true);
			setClockInTooltipMsg('');
			return;
		}

		const now = new Date();
		const lastLog = attendanceLogs[attendanceLogs.length - 1];
		const lastLogTime = new Date(lastLog?.time);

		const totalClockIns = attendanceLogs.filter(
			(log) => log.type === 'clockIn'
		).length;
		const totalClockOuts = attendanceLogs.filter(
			(log) => log.type === 'clockOut'
		).length;

		if (lastLog?.type === 'start-break') {
			setClockedIn(true);
			setIsOnBreak(true); // you need to add this state to track break status
			setCanClockIn(false);
			setCanClockOut(false);
			setClockOutTooltipMsg('You are on a break');
		} else if (lastLog?.type === 'end-break') {
			setClockedIn(true);
			setCanClockIn(false);
			setCanClockOut(true);
			setIsOnBreak(false);
			setClockOutTooltipMsg('');
			// then apply your normal clock-in conditions here again or just reuse existing logic
		}

		if (lastLog?.type === 'clockIn') {
			setClockedIn(true);
			setCanClockIn(false);
			setClockInTooltipMsg('Already clocked in');

			if (totalClockOuts >= clockOutLimit) {
				setCanClockOut(false);
				setClockOutTooltipMsg(
					`You have reached the daily Clock Out limit (${clockOutLimit})`
				);
			} else if (addMinutes(lastLogTime, clockOutDelay) > now) {
				const waitMins = Math.ceil(
					(addMinutes(lastLogTime, clockOutDelay).getTime() - now.getTime()) /
						60000
				);
				setCanClockOut(false);
				setClockOutTooltipMsg(
					`Please wait ${waitMins} more minute(s) before Clocking Out`
				);
			} else {
				setCanClockOut(true);
				setClockOutTooltipMsg('');
			}
		} else if (lastLog?.type === 'clockOut') {
			setClockedIn(false);
			setCanClockOut(false);
			setClockOutTooltipMsg('Already clocked out');

			if (totalClockIns >= clockInLimit) {
				setCanClockIn(false);
				setClockInTooltipMsg(
					`You have reached the daily Clock In limit (${clockInLimit})`
				);
			} else if (addMinutes(lastLogTime, clockInDelay) > now) {
				const waitMins = Math.ceil(
					(addMinutes(lastLogTime, clockInDelay).getTime() - now.getTime()) /
						60000
				);
				setCanClockIn(false);
				setClockInTooltipMsg(
					`Please wait ${waitMins} more minute(s) before Clocking In`
				);
			} else {
				setCanClockIn(true);
				setClockInTooltipMsg('');
			}
		}
	};

	const getLogDetails = (type) => {
		switch (type) {
			case 'clockIn':
				return {
					title: 'Clock In',
					color: 'bg-green-500',
				};
			case 'clockOut':
				return {
					title: 'Clock Out',
					color: 'bg-red-500',
				};
			case 'start-break':
				return {
					title: 'Start Break',
					color: 'bg-yellow-500',
				};
			case 'end-break':
				return {
					title: 'End Break',
					color: 'bg-blue-500',
				};
			default:
				return {
					title: type,
					color: 'bg-gray-500',
				};
		}
	};

	const onRefresh = () => {
		dispatch(getAttendanceLogs());
		dispatch(getStatus());
	};

	useEffect(() => {
		checkClockingConditions();
	}, [
		attendanceLogs,
		clockInDelay,
		clockOutDelay,
		clockInLimit,
		clockOutLimit,
	]);
	useEffect(() => {
		if (!attendanceLogs) return;
		// Reverse iterate and grab the last break event
		const lastBreakEvent = [...attendanceLogs]
			.reverse()
			.find((log) => log.type === 'start-break' || log.type === 'end-break');
		setIsOnBreak(lastBreakEvent?.type === 'start-break');
	}, [attendanceLogs]);

	useEffect(() => {
		dispatch(getAttendanceLogs());
	}, [dispatch]);

	useEffect(() => {
		dispatch(getStatus());
	}, [dispatch]);

	useEffect(() => {
		checkClockingConditions(); // initial check

		// Set up interval only if delay active (clockIn or clockOut)
		const now = new Date();
		const lastLog = attendanceLogs?.[attendanceLogs.length - 1];
		if (!lastLog) return; // no logs, no timer needed

		let delayEndTime;

		if (lastLog.type === 'clockIn') {
			delayEndTime = addMinutes(new Date(lastLog.time), clockOutDelay);
		} else if (lastLog.type === 'clockOut') {
			delayEndTime = addMinutes(new Date(lastLog.time), clockInDelay);
		} else {
			return;
		}

		if (delayEndTime <= now) {
			return; // delay already passed, no timer needed
		}

		const timer = setInterval(() => {
			checkClockingConditions();
		}, 1000 * 30); // every 30 seconds (or 60000 for every minute)

		return () => clearInterval(timer);
	}, [
		attendanceLogs,
		clockInDelay,
		clockOutDelay,
		clockInLimit,
		clockOutLimit,
	]);

	// if (isLoading) return <SimpleLoader />;

	const handleClockIn = async () => {
		await dispatch(clockIn());
		onRefresh();
	};

	const handleClockOut = async () => {
		await dispatch(clockOut());
		onRefresh();
	};

	const handleEndBreak = async () => {
		await dispatch(endBreak());
		onRefresh();
	};
	const handleStartBreak = async () => {
		await dispatch(startBreak());
		onRefresh();
	};

	return (
		<div className="flex-1 min-w-[280px] max-w-full sm:max-w-auto bg-white shadow-sm border border-gray-200 rounded-xl p-3 space-y-3 text-sm min-h-[200px]">
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
				<h2 className="text-base font-semibold text-gray-800 flex items-center gap-2 h-8">
					Mark Attendance
				</h2>
				<div className="h-8 flex items-center gap-2">
					{isLoading ? <SimpleLoader /> : null}
					{!clockedIn ? (
						<Tooltip>
							<TooltipTrigger>
								<ConfirmDialog
									renderTrigger={
										<Button
											variant="outline"
											className="bg-green-100 text-green-800 hover:bg-green-200"
											disabled={isLoading || !canClockIn}
										>
											Clock In
										</Button>
									}
									title="Are you sure you want to clock in?"
									description={`You will not be able to Clock Out for the next ${clockInDelay} minutes`}
									confirmTextClassName={
										'bg-green-100 text-green-800 hover:bg-green-200'
									}
									confirmText="Clock In"
									cancelText="Cancel"
									onConfirm={handleClockIn}
								/>
							</TooltipTrigger>
							{!canClockIn && clockInTooltipMsg.length ? (
								<TooltipContent className="bg-red-100 text-red-800">
									<p>{clockInTooltipMsg}</p>
								</TooltipContent>
							) : null}
						</Tooltip>
					) : (
						<>
							{isOnBreak ? (
								<Button
									onClick={handleEndBreak}
									variant="outline"
									size="icon"
									disabled={isLoading}
								>
									<Laptop />
								</Button>
							) : (
								<Button
									onClick={handleStartBreak}
									variant="outline"
									size="icon"
									disabled={isLoading}
								>
									<CoffeeIcon />
								</Button>
							)}
							<Tooltip>
								<TooltipTrigger>
									<ConfirmDialog
										renderTrigger={
											<Button
												variant="outline"
												className="bg-red-100 text-red-800 hover:bg-red-200"
												disabled={isLoading || !canClockOut}
											>
												Clock Out
											</Button>
										}
										title="Are you sure you want to clock out?"
										description={`You will not be able to Clock In for the next ${clockOutDelay} minutes`}
										confirmTextClassName={
											'bg-red-100 text-red-800 hover:bg-red-200'
										}
										confirmText="Clock Out"
										cancelText="Cancel"
										onConfirm={handleClockOut}
									/>
								</TooltipTrigger>
								{!canClockOut && clockOutTooltipMsg.length ? (
									<TooltipContent className="bg-red-100 text-red-800">
										<p>{clockOutTooltipMsg}</p>
									</TooltipContent>
								) : null}
							</Tooltip>
							<Button
								onClick={onRefresh}
								variant="outline"
								size="icon"
								disabled={isLoading}
							>
								<RefreshCwIcon />
							</Button>
						</>
					)}
				</div>
			</div>

			<div className="max-h-[150px] overflow-y-auto space-y-2 border-t pt-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
				{attendanceLogs?.map((log, index) => (
					<div key={index} className="flex ml-1 align-middle">
						<div
							className={`left-0 my-1 w-1.5 h-auto rounded-full ${getLogDetails(log.type).color}`}
						/>
						<Alert className="pl-3 py-1 border-0 ">
							<AlertTitle className="font-semibold">
								{getLogDetails(log.type).title}
							</AlertTitle>
							<AlertDescription className="text-xs text-gray-500">
								{new Date(log.time).toLocaleString()}
							</AlertDescription>
						</Alert>
					</div>
				))}
			</div>
		</div>
	);
};

export default MarkAttendance;
