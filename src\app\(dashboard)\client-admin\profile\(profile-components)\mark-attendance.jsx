import React, { useState, useEffect } from 'react';
import { CoffeeIcon, RefreshCwIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
	Tooltip,
	TooltipContent,
	TooltipTrigger,
} from '@/components/ui/tooltip';
import { format, addMinutes, addHours } from 'date-fns';
import ConfirmDialog from '@/components/confirm-dialog';

const MarkAttendance = () => {
	const [attendanceLogs, setAttendanceLogs] = useState([
		{ type: 'Clock In', time: new Date('2025-05-09T05:00:00') },
		{ type: 'Clock Out', time: new Date('2025-05-09T06:00:00') },
		{ type: 'Clock In', time: new Date('2025-05-09T18:10:00') },
		{ type: 'Clock Out', time: new Date('2025-05-09T22:00:00') },
	]);
	const [clockedIn, setClockedIn] = useState(false);
	const [canClockIn, setCanClockIn] = useState(true);
	const [canClockOut, setCanClockOut] = useState(false);

	const checkClockingConditions = () => {
		const lastLog = attendanceLogs[attendanceLogs.length - 1];

		// let lastLog = lastLogTime;
		let lastLogTime = lastLog.time;

		if (lastLog?.type === 'Clock In') {
			setClockedIn(true);
			setCanClockIn(false);
			let lastLogTimePlusHalfHour = addMinutes(lastLogTime, 30);
			setCanClockOut(new Date() >= lastLogTimePlusHalfHour);
		} else if (lastLog?.type === 'Clock Out') {
			setClockedIn(false); // because we are currently clocked out

			setCanClockOut(false); // because we are already clocked out

			console.log('last log time', lastLogTime);
			console.log(' added hjours ', addHours(lastLogTime, 2));

			setCanClockIn(new Date() >= addHours(lastLogTime, 2));
		}
	};

	const getBarColor = (type) => {
		switch (type) {
			case 'Clock In':
				return 'bg-green-500';
				break;
			case 'Clock Out':
				return 'bg-red-500';
				break;
			case 'Break':
				return 'bg-yellow-500';
				break;
			default:
				return 'bg-gray-300';
		}
	};

	const onClockIn = () => {
		setAttendanceLogs([
			...attendanceLogs,
			{ type: 'Clock In', time: new Date() },
		]);
	};

	const onClockOut = () => {
		setAttendanceLogs([
			...attendanceLogs,
			{ type: 'Clock Out', time: new Date() },
		]);
	};

	const onBreak = () => {
		setAttendanceLogs([...attendanceLogs, { type: 'Break', time: new Date() }]);
	};

	const onRefresh = () => {
		// data fetch here
		setAttendanceLogs([
			{ type: 'Clock In', time: new Date('2025-05-09T05:00:00') },
			{ type: 'Clock Out', time: new Date('2025-05-09T06:00:00') },
			{ type: 'Clock In', time: new Date('2025-05-09T18:10:00') },
		]);
	};

	useEffect(() => {
		checkClockingConditions();
	}, [attendanceLogs]);

	const handleClockIn = () => {
		onClockIn();
		setClockedIn(true);
	};

	const handleClockOut = () => {
		onClockOut();
		setClockedIn(false);
	};

	return (
		<div className="flex-1 min-w-[280px] max-w-full sm:max-w-auto bg-white shadow-sm border border-gray-200 rounded-xl p-3 space-y-3 text-sm min-h-[200px]">
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
				<h2 className="text-base font-semibold text-gray-800 flex items-center gap-2 h-8">
					Mark Attendance
				</h2>
				<div className="h-8 flex items-center gap-2">
					{!clockedIn ? (
						<Tooltip>
							<TooltipTrigger>
								<ConfirmDialog
									renderTrigger={
										<Button
											variant="outline"
											className="bg-green-100 text-green-800 hover:bg-green-200"
											disabled={!canClockIn}
										>
											Clock In
										</Button>
									}
									title="Are you sure you want to clock in?"
									description="You will not be able to Clock Out for the next 15 minutes"
									confirmTextClassName={
										'bg-green-100 text-green-800 hover:bg-green-200'
									}
									confirmText="Clock In"
									cancelText="Cancel"
									onConfirm={handleClockIn}
								/>
							</TooltipTrigger>
							{canClockIn ? null : (
								<TooltipContent className="bg-red-100 text-red-800 ">
									<p>You need to wait for 2 hours</p>
								</TooltipContent>
							)}
						</Tooltip>
					) : (
						<>
							<Button onClick={onBreak} variant="outline" size="icon">
								<CoffeeIcon />
							</Button>
							<Tooltip>
								<TooltipTrigger>
									<ConfirmDialog
										renderTrigger={
											<Button
												variant="outline"
												className="bg-red-100 text-red-800 hover:bg-red-200"
												disabled={!canClockOut}
											>
												{' '}
												Clock Out
											</Button>
										}
										title="Are you sure you want to clock out?"
										description="You will not be able to Clock In for the next 2 hours"
										confirmTextClassName={
											'bg-red-100 text-red-800 hover:bg-red-200'
										}
										confirmText="Clock Out"
										cancelText="Cancel"
										onConfirm={handleClockOut}
									/>
								</TooltipTrigger>
								{canClockOut ? null : (
									<TooltipContent className="bg-red-100 text-red-800 ">
										<p>You need to wait for 15 minutes</p>
									</TooltipContent>
								)}
							</Tooltip>

							<Button onClick={onRefresh} variant="outline" size="icon">
								<RefreshCwIcon />
							</Button>
						</>
					)}
				</div>
			</div>

			{/* Attendance Logs stay the same */}
			<div className="max-h-[150px] overflow-y-auto space-y-2 border-t pt-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
				{/* for debug stuff */}
				{/* {clockedIn ? 'clocked in' : 'clocked out'}
				<br></br>
				{canClockIn ? 'can clock in' : 'cannot clock in'}
				<br></br>
				{canClockOut ? 'can clock out' : 'cannot clock out'} */}

				{attendanceLogs.map((log, index) => (
					<div key={index} className="flex ml-1 align-middle">
						<div
							className={`left-0 my-1 w-1.5 h-auto rounded-full ${getBarColor(
								log.type
							)}`}
						></div>
						<Alert className="pl-3 py-1 border-0 ">
							<AlertTitle className="font-semibold">{log.type}</AlertTitle>
							<AlertDescription className="text-xs text-gray-500">
								{log.time.toLocaleString()}
							</AlertDescription>
						</Alert>
					</div>
				))}
			</div>
		</div>
	);
};

export default MarkAttendance;
