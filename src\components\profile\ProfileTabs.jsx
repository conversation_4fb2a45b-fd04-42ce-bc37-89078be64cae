import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { User, Users, GraduationCap, Briefcase, Phone } from 'lucide-react';
import { PersonalTab } from './tabs/PersonalTab';
import { FamilyTab } from './tabs/FamilyTab';
import { EducationTab } from './tabs/EducationTab';
import { ExperienceTab } from './tabs/ExperienceTab';
import { ContactTab } from './tabs/ContactTab';

export function ProfileTabs({ 
	personalDetails, 
	family, 
	education, 
	experience, 
	contact, 
	onEditSection,
	onAddChild 
}) {
	return (
		<Tabs defaultValue="personal" className="w-full">
			<TabsList className="grid grid-cols-5 mb-8">
				<TabsTrigger value="personal" className="flex items-center gap-2">
					<User className="h-4 w-4" />
					<span className="hidden sm:inline">Personal</span>
				</TabsTrigger>
				<TabsTrigger value="family" className="flex items-center gap-2">
					<Users className="h-4 w-4" />
					<span className="hidden sm:inline">Family</span>
				</TabsTrigger>
				<TabsTrigger value="education" className="flex items-center gap-2">
					<GraduationCap className="h-4 w-4" />
					<span className="hidden sm:inline">Education</span>
				</TabsTrigger>
				<TabsTrigger value="experience" className="flex items-center gap-2">
					<Briefcase className="h-4 w-4" />
					<span className="hidden sm:inline">Experience</span>
				</TabsTrigger>
				<TabsTrigger value="contact" className="flex items-center gap-2">
					<Phone className="h-4 w-4" />
					<span className="hidden sm:inline">Contact</span>
				</TabsTrigger>
			</TabsList>

			<TabsContent value="personal">
				<PersonalTab 
					personalDetails={personalDetails} 
					onEditSection={onEditSection} 
				/>
			</TabsContent>

			<TabsContent value="family">
				<FamilyTab 
					family={family} 
					onEditSection={onEditSection}
					onAddChild={onAddChild}
				/>
			</TabsContent>

			<TabsContent value="education">
				<EducationTab 
					education={education} 
					onEditSection={onEditSection} 
				/>
			</TabsContent>

			<TabsContent value="experience">
				<ExperienceTab 
					experience={experience} 
					onEditSection={onEditSection} 
				/>
			</TabsContent>

			<TabsContent value="contact">
				<ContactTab 
					contact={contact} 
					onEditSection={onEditSection} 
				/>
			</TabsContent>
		</Tabs>
	);
}
