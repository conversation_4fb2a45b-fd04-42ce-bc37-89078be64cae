/**
 * Utility functions for edit request management
 */

/**
 * Calculate age from date of birth
 * @param {string} dob - Date of birth in YYYY-MM-DD format
 * @returns {number} Age in years
 */
export const calculateAge = (dob) => {
	if (!dob) return 0;

	const birthDate = new Date(dob);
	const today = new Date();

	let age = today.getFullYear() - birthDate.getFullYear();
	const monthDiff = today.getMonth() - birthDate.getMonth();

	// If birthday hasn't occurred this year yet, subtract 1
	if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
		age--;
	}

	return Math.max(0, age); // Ensure age is not negative
};

/**
 * Check if there's a pending edit request for a specific section and user
 * @param {Array} editRequests - Array of edit requests from Redux store
 * @param {string} userId - User ID
 * @param {string} section - Section name (personal, family, etc.)
 * @returns {Object|null} Pending request if found, null otherwise
 */
export const findPendingRequest = (editRequests, userId, section) => {
	if (!editRequests || !Array.isArray(editRequests)) return null;

	return editRequests.find(
		request =>
			request.userId === userId &&
			request.section === section &&
			request.status === 'PENDING'
	) || null;
};

/**
 * Compare two data objects to detect changes
 * Only compares fields that exist in newData with oldData
 * @param {Object} oldData - Original data
 * @param {Object} newData - New data
 * @returns {Array} Array of change descriptions
 */
export const detectChanges = (oldData, newData) => {
	const changes = [];

	if (!newData || typeof newData !== 'object') return changes;
	if (!oldData) return ['New data added'];

	// Helper function to compare values
	const compareValues = (key, oldVal, newVal) => {
		// Normalize values for comparison (handle null, undefined, empty string)
		const normalizeValue = (val) => {
			if (val === null || val === undefined || val === '') return '';
			return val;
		};

		const normalizedOld = normalizeValue(oldVal);
		const normalizedNew = normalizeValue(newVal);

		if (normalizedOld !== normalizedNew) {
			if (normalizedOld === '' && normalizedNew !== '') {
				changes.push(`Added ${key}: ${newVal}`);
			} else if (normalizedOld !== '' && normalizedNew === '') {
				changes.push(`Removed ${key}`);
			} else {
				changes.push(`Changed ${key}: ${oldVal} → ${newVal}`);
			}
		}
	};

	// Only iterate through keys that exist in newData
	for (const key of Object.keys(newData)) {
		const oldVal = oldData[key];
		const newVal = newData[key];

		// Handle arrays (for children, education, experience, contact)
		if (Array.isArray(newVal)) {
			const oldArray = Array.isArray(oldVal) ? oldVal : [];
			const newArray = newVal;

			// Deep comparison for arrays
			const arraysEqual = (arr1, arr2) => {
				if (arr1.length !== arr2.length) return false;

				// Sort arrays by a consistent key for comparison (if objects have consistent structure)
				const sortArray = (arr) => {
					if (arr.length === 0) return arr;
					if (typeof arr[0] === 'object' && arr[0] !== null) {
						// For objects, sort by the first string property found
						const firstStringKey = Object.keys(arr[0]).find(k => typeof arr[0][k] === 'string');
						if (firstStringKey) {
							return [...arr].sort((a, b) => (a[firstStringKey] || '').localeCompare(b[firstStringKey] || ''));
						}
					}
					return arr;
				};

				const sortedArr1 = sortArray(arr1);
				const sortedArr2 = sortArray(arr2);

				return JSON.stringify(sortedArr1) === JSON.stringify(sortedArr2);
			};

			if (!arraysEqual(oldArray, newArray)) {
				if (oldArray.length !== newArray.length) {
					changes.push(`${key}: ${oldArray.length} → ${newArray.length} entries`);
				} else {
					changes.push(`Modified ${key} entries`);
				}
			}
		} else {
			compareValues(key, oldVal, newVal);
		}
	}

	return changes;
};

/**
 * Check if there are any actual changes between old and new data
 * Only compares fields that exist in newData with oldData
 * @param {Object} oldData - Original data
 * @param {Object} newData - New data
 * @returns {boolean} True if there are changes, false otherwise
 */
export const hasChanges = (oldData, newData) => {
	const changes = detectChanges(oldData, newData);
	return changes.length > 0;
};

/**
 * Format changes for display in confirmation dialog
 * @param {Array} changes - Array of change descriptions
 * @returns {string} Formatted changes string
 */
export const formatChangesForDisplay = (changes) => {
	if (!changes || changes.length === 0) return 'No changes detected';

	if (changes.length === 1) return changes[0];

	if (changes.length <= 3) {
		return changes.join(', ');
	}

	return `${changes.slice(0, 2).join(', ')} and ${changes.length - 2} more changes`;
};

/**
 * Prepare data for submission based on section type
 * @param {string} section - Section name
 * @param {Object} formData - Form data from react-hook-form
 * @param {Object} currentData - Current data from profile
 * @returns {Object} Prepared data for submission
 */
export const prepareSubmissionData = (section, formData, currentData) => {
	switch (section) {
		case 'personal':
			return {
				...formData,
				// Ensure proper data types
				dob: formData.dob || currentData?.dob,
			};

		case 'family':
			return {
				maritalStatus: formData.maritalStatus || currentData?.maritalStatus,
				spouseName: formData.spouseName || currentData?.spouseName || '',
				spouseEmploymentStatus: formData.spouseEmploymentStatus || currentData?.spouseEmploymentStatus || '',
				children: formData.children || currentData?.children || [],
			};

		case 'education':
			return {
				education: formData.education || [],
			};

		case 'experience':
			return {
				experience: formData.experience || [],
			};

		case 'contact':
			return {
				contact: formData.contact || [],
			};

		default:
			return formData;
	}
};

/**
 * Get current data for a specific section
 * @param {Object} userData - Complete user data from profile
 * @param {string} section - Section name
 * @returns {Object} Section-specific data
 */
export const getSectionData = (userData, section) => {
	if (!userData) return {};

	switch (section) {
		case 'personal':
			return {
				nameOnNRIC: userData.nameOnNRIC || '',
				dob: userData.dob || '',
				gender: userData.gender || '',
				icFinNumber: userData.icFinNumber || '',
				religion: userData.religion || '',
				race: userData.race || '',
			};

		case 'family':
			return {
				maritalStatus: userData.maritalStatus || '',
				spouseName: userData.spouseName || '',
				spouseEmploymentStatus: userData.spouseEmploymentStatus || '',
				children: userData.children || [],
			};

		case 'education':
			return {
				education: userData.education || [],
			};

		case 'experience':
			return {
				experience: userData.experience || [],
			};

		case 'contact':
			return {
				contact: userData.contact || [],
			};

		default:
			return {};
	}
};

/**
 * Add calculated age to children array
 * @param {Array} children - Array of children objects
 * @returns {Array} Children array with calculated ages
 */
export const addCalculatedAges = (children) => {
	if (!Array.isArray(children)) return [];

	return children.map(child => ({
		...child,
		age: calculateAge(child.dob),
	}));
};
