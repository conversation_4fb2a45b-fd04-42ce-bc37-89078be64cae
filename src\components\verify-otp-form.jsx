'use client';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useForm } from 'react-hook-form';
import {
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from './ui/form';
import Link from 'next/link';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { Loader2 } from 'lucide-react';
import { verifyEmailSchema } from '@/lib/schemas/authenticationSchema';
import { zodResolver } from '@hookform/resolvers/zod';
import {
	InputOTP,
	InputOTPGroup,
	InputOTPSeparator,
	InputOTPSlot,
} from './ui/input-otp';
import { verifyEmail } from '@/lib/features/auth/authSlice';
import { LoadingSubmitButton } from './loading-component';

export default function VerifyOTPForm({ className, ...props }) {
	const form = useForm({
		resolver: zodResolver(verifyEmailSchema),
		defaultValues: {
			verificationToken: '',
		},
	});
	const { isLoading, userEmail } = useAppSelector(
		(store) => store.auth
	);
	const dispatch = useAppDispatch();
	const onSubmit = (data) => {
		console.log(`onSubmit - data:`, data);
		dispatch(verifyEmail({ ...data, email: userEmail }));
	};
	return (
		<Form>
			<Form {...form}>
				<form
					className={cn('flex flex-col gap-6', className)}
					{...props}
					onSubmit={form.handleSubmit(onSubmit)}
				>
					<div className="flex flex-col items-center gap-2 ">
						<h1 className="text-2xl font-bold">Verify your account</h1>
						{/* <p className="text-sm text-foreground w-full">
							Enter the one time password sent to <br /> {userEmail}
						</p> */}
					</div>
					<div className="grid gap-6">
						<FormField
							control={form.control}
							name="verificationToken"
							render={({ field }) => (
								<FormItem>
									<FormLabel>One-Time Password</FormLabel>
									<FormControl>
										<InputOTP
											maxLength={6}
											{...field}
											// containerClassName="justify-center gap-4"
										>
											<InputOTPGroup>
												<InputOTPSlot
													className="border border-muted-foreground"
													index={0}
												/>
											</InputOTPGroup>
											<InputOTPSeparator />
											<InputOTPGroup>
												<InputOTPSlot
													className="border border-muted-foreground"
													index={1}
												/>
											</InputOTPGroup>
											<InputOTPSeparator />
											<InputOTPGroup>
												<InputOTPSlot
													className="border border-muted-foreground"
													index={2}
												/>
											</InputOTPGroup>
											<InputOTPSeparator />
											<InputOTPGroup>
												<InputOTPSlot
													className="border border-muted-foreground"
													index={3}
												/>
											</InputOTPGroup>
											<InputOTPSeparator />
											<InputOTPGroup>
												<InputOTPSlot
													className="border border-muted-foreground"
													index={4}
												/>
											</InputOTPGroup>
											<InputOTPSeparator />
											<InputOTPGroup>
												<InputOTPSlot
													className="border border-muted-foreground"
													index={5}
												/>
											</InputOTPGroup>
										</InputOTP>
									</FormControl>
									<FormDescription>
										Please enter the one-time password sent to <br />{' '}
										<span className="font-semibold">{userEmail}</span>.
									</FormDescription>
									<FormMessage />
								</FormItem>
							)}
						/>

						<LoadingSubmitButton
							isLoading={isLoading}
							buttonLoadingText={'Verifying...'}
							buttonText={'Verify OTP'}
						/>
					</div>
					<div className="text-center text-sm">
						Did&apos;t receive OTP?{' '}
						<Link href="/login" className="underline underline-offset-4">
							Resend
						</Link>
					</div>
				</form>
			</Form>
		</Form>
	);
}
