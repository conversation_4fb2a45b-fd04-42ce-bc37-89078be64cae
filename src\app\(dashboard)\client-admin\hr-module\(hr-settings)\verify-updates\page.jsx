'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { CheckCircle, Clock, XCircle, FileText, Filter } from 'lucide-react';
import EditRequestsTable from './(edit-requests-table)/edit-requests-table';
import EditRequestFilters from './(edit-requests-filters)/edit-requests-filters';
import CompareDataDialog from './(compare-data-dialog)/compare-data-dialog';

const VerifyUpdates = () => {
	const [selectedRequest, setSelectedRequest] = useState(null);
	const [showCompareDialog, setShowCompareDialog] = useState(false);
	const [filters, setFilters] = useState({
		status: 'all',
		section: 'all',
		employee: 'all',
		dateRange: null,
	});

	// Dummy data for edit requests
	const editRequests = [
		{
			_id: '507f1f77bcf86cd799439011',
			userId: {
				_id: '507f1f77bcf86cd799439012',
				nameOnNRIC: 'John Doe',
				employeeOrgId: 'EMP001',
				email: '<EMAIL>',
			},
			section: 'personal-details',
			oldData: {
				nameOnNRIC: 'John Doe',
				dateOfBirth: '1990-05-15',
				gender: 'male',
				maritalStatus: 'single',
			},
			newData: {
				nameOnNRIC: 'John Michael Doe',
				dateOfBirth: '1990-05-15',
				gender: 'male',
				maritalStatus: 'married',
			},
			status: 'pending',
			submittedAt: '2024-01-15T10:30:00Z',
			submittedBy: {
				_id: '507f1f77bcf86cd799439012',
				nameOnNRIC: 'John Doe',
			},
		},
		{
			_id: '507f1f77bcf86cd799439013',
			userId: {
				_id: '507f1f77bcf86cd799439014',
				nameOnNRIC: 'Jane Smith',
				employeeOrgId: 'EMP002',
				email: '<EMAIL>',
			},
			section: 'contact-details',
			oldData: {
				mobile: '+1234567890',
				address: '123 Old Street, City',
				emergencyContact: {
					name: 'Old Contact',
					phone: '+0987654321',
				},
			},
			newData: {
				mobile: '+1234567899',
				address: '456 New Avenue, City',
				emergencyContact: {
					name: 'New Contact',
					phone: '+0987654322',
				},
			},
			status: 'approved',
			submittedAt: '2024-01-14T14:20:00Z',
			submittedBy: {
				_id: '507f1f77bcf86cd799439014',
				nameOnNRIC: 'Jane Smith',
			},
			approvedAt: '2024-01-15T09:15:00Z',
			approvedBy: {
				_id: '507f1f77bcf86cd799439015',
				nameOnNRIC: 'HR Manager',
			},
		},
		{
			_id: '507f1f77bcf86cd799439016',
			userId: {
				_id: '507f1f77bcf86cd799439017',
				nameOnNRIC: 'Mike Johnson',
				employeeOrgId: 'EMP003',
				email: '<EMAIL>',
			},
			section: 'education-details',
			oldData: {
				education: [
					{
						instituteName: 'Old University',
						qualification: 'UNDER_GRADUATE',
						grade: 'B+',
						startDate: '2010-09-01',
						endDate: '2014-06-30',
					},
				],
			},
			newData: {
				education: [
					{
						instituteName: 'New University',
						qualification: 'POST_GRADUATE',
						grade: 'A',
						startDate: '2014-09-01',
						endDate: '2016-06-30',
					},
				],
			},
			status: 'rejected',
			submittedAt: '2024-01-13T16:45:00Z',
			submittedBy: {
				_id: '507f1f77bcf86cd799439017',
				nameOnNRIC: 'Mike Johnson',
			},
			rejectedAt: '2024-01-14T11:30:00Z',
			rejectedBy: {
				_id: '507f1f77bcf86cd799439015',
				nameOnNRIC: 'HR Manager',
			},
			rejectionReason: 'Insufficient documentation provided',
		},
		{
			_id: '507f1f77bcf86cd799439018',
			userId: {
				_id: '507f1f77bcf86cd799439019',
				nameOnNRIC: 'Sarah Wilson',
				employeeOrgId: 'EMP004',
				email: '<EMAIL>',
			},
			section: 'employment-details',
			oldData: {
				businessUnit: 'IT Department',
				department: 'Software Development',
				designation: 'Junior Developer',
				employmentType: 'full-time',
				workSchedule: 'generic',
			},
			newData: {
				businessUnit: 'IT Department',
				department: 'Software Development',
				designation: 'Senior Developer',
				employmentType: 'full-time',
				workSchedule: 'generic',
			},
			status: 'pending',
			submittedAt: '2024-01-16T11:15:00Z',
			submittedBy: {
				_id: '507f1f77bcf86cd799439019',
				nameOnNRIC: 'Sarah Wilson',
			},
		},
		{
			_id: '507f1f77bcf86cd799439020',
			userId: {
				_id: '507f1f77bcf86cd799439021',
				nameOnNRIC: 'David Brown',
				employeeOrgId: 'EMP005',
				email: '<EMAIL>',
			},
			section: 'family-details',
			oldData: {
				spouse: null,
				children: [],
			},
			newData: {
				spouse: {
					name: 'Emily Brown',
					occupation: 'Teacher',
					dateOfBirth: '1992-03-20',
				},
				children: [
					{
						name: 'Alex Brown',
						dateOfBirth: '2020-08-15',
						gender: 'male',
					},
				],
			},
			status: 'pending',
			submittedAt: '2024-01-16T09:30:00Z',
			submittedBy: {
				_id: '507f1f77bcf86cd799439021',
				nameOnNRIC: 'David Brown',
			},
		},
	];

	const handleCompareData = (request) => {
		setSelectedRequest(request);
		setShowCompareDialog(true);
	};

	const handleApproveRequest = (requestId) => {
		// TODO: Implement approve logic
		console.log('Approving request:', requestId);
	};

	const handleRejectRequest = (requestId, reason) => {
		// TODO: Implement reject logic
		console.log('Rejecting request:', requestId, 'Reason:', reason);
	};

	// Statistics for the dashboard
	const stats = {
		total: editRequests.length,
		pending: editRequests.filter((req) => req.status === 'pending').length,
		approved: editRequests.filter((req) => req.status === 'approved').length,
		rejected: editRequests.filter((req) => req.status === 'rejected').length,
	};

	return (
		<section className="w-full h-full p-4">
			<header className="flex flex-col gap-2">
				<h1 className="text-xl md:text-2xl font-semibold">Verify Updates</h1>
				<p className="font-medium text-gray-500">
					Review and approve employee data update requests.
				</p>
				<Separator />
			</header>

			{/* Statistics Cards */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total Requests</CardTitle>
						<FileText className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.total}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Pending</CardTitle>
						<Clock className="h-4 w-4 text-yellow-600" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Approved</CardTitle>
						<CheckCircle className="h-4 w-4 text-green-600" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-green-600">{stats.approved}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Rejected</CardTitle>
						<XCircle className="h-4 w-4 text-red-600" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
					</CardContent>
				</Card>
			</div>

			{/* Main Content */}
			<main className="grid flex-1 items-start gap-4 md:gap-8 w-full mt-6">
				<Card>
					<CardHeader>
						<CardTitle>Edit Requests</CardTitle>
						<CardDescription>
							Review and manage employee data update requests.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<EditRequestFilters filters={filters} setFilters={setFilters} />
						<EditRequestsTable
							data={editRequests}
							filters={filters}
							onCompareData={handleCompareData}
							onApproveRequest={handleApproveRequest}
							onRejectRequest={handleRejectRequest}
						/>
					</CardContent>
				</Card>
			</main>

			{/* Compare Data Dialog */}
			{showCompareDialog && selectedRequest && (
				<CompareDataDialog
					request={selectedRequest}
					open={showCompareDialog}
					onOpenChange={setShowCompareDialog}
					onApprove={handleApproveRequest}
					onReject={handleRejectRequest}
				/>
			)}
		</section>
	);
};

export default VerifyUpdates;
