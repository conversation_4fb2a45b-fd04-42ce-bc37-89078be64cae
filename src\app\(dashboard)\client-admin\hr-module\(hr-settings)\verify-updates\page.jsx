'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { CheckCircle, Clock, XCircle, FileText, Filter } from 'lucide-react';
import EditRequestsTable from './(edit-requests-table)/edit-requests-table';
import EditRequestFilters from './(edit-requests-filters)/edit-requests-filters';
import CompareDataDialog from './(compare-data-dialog)/compare-data-dialog';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import {
	fetchEditRequests,
	updateEditRequestStatus,
} from '@/lib/features/employees/editRequestSlice';

const VerifyUpdates = () => {
	const dispatch = useAppDispatch();
	const { editRequests, isLoading } = useAppSelector((state) => state.editRequest);

	const [selectedRequest, setSelectedRequest] = useState(null);
	const [showCompareDialog, setShowCompareDialog] = useState(false);
	const [filters, setFilters] = useState({
		status: 'all',
		section: 'all',
		employee: 'all',
		dateRange: null,
	});

	// Fetch edit requests on component mount
	useEffect(() => {
		dispatch(fetchEditRequests());
	}, [dispatch]);

	// Use actual edit requests data or fallback to empty array
	const editRequestsData = editRequests || [];

	const handleCompareData = (request) => {
		setSelectedRequest(request);
		setShowCompareDialog(true);
	};

	const handleApproveRequest = (requestId) => {
		dispatch(updateEditRequestStatus({
			editRequestId: requestId,
			status: 'approved'
		}));
	};

	const handleRejectRequest = (requestId, reason) => {
		dispatch(updateEditRequestStatus({
			editRequestId: requestId,
			status: 'rejected',
			rejectionReason: reason
		}));
	};

	// Statistics for the dashboard
	const stats = {
		total: editRequestsData.length,
		pending: editRequestsData.filter((req) => req.status === 'pending').length,
		approved: editRequestsData.filter((req) => req.status === 'approved').length,
		rejected: editRequestsData.filter((req) => req.status === 'rejected').length,
	};

	return (
		<section className="w-full h-full p-4">
			<header className="flex flex-col gap-2">
				<h1 className="text-xl md:text-2xl font-semibold">Verify Updates</h1>
				<p className="font-medium text-gray-500">
					Review and approve employee data update requests.
				</p>
				<Separator />
			</header>

			{/* Statistics Cards */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Total Requests</CardTitle>
						<FileText className="h-4 w-4 text-muted-foreground" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold">{stats.total}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Pending</CardTitle>
						<Clock className="h-4 w-4 text-yellow-600" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-yellow-600">{stats.pending}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Approved</CardTitle>
						<CheckCircle className="h-4 w-4 text-green-600" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-green-600">{stats.approved}</div>
					</CardContent>
				</Card>
				<Card>
					<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
						<CardTitle className="text-sm font-medium">Rejected</CardTitle>
						<XCircle className="h-4 w-4 text-red-600" />
					</CardHeader>
					<CardContent>
						<div className="text-2xl font-bold text-red-600">{stats.rejected}</div>
					</CardContent>
				</Card>
			</div>

			{/* Main Content */}
			<main className="grid flex-1 items-start gap-4 md:gap-8 w-full mt-6">
				<Card>
					<CardHeader>
						<CardTitle>Edit Requests</CardTitle>
						<CardDescription>
							Review and manage employee data update requests.
						</CardDescription>
					</CardHeader>
					<CardContent>
						<EditRequestFilters filters={filters} setFilters={setFilters} />
						<EditRequestsTable
							data={editRequestsData}
							filters={filters}
							isLoading={isLoading}
							onCompareData={handleCompareData}
							onApproveRequest={handleApproveRequest}
							onRejectRequest={handleRejectRequest}
						/>
					</CardContent>
				</Card>
			</main>

			{/* Compare Data Dialog */}
			{showCompareDialog && selectedRequest && (
				<CompareDataDialog
					request={selectedRequest}
					open={showCompareDialog}
					onOpenChange={setShowCompareDialog}
					onApprove={handleApproveRequest}
					onReject={handleRejectRequest}
				/>
			)}
		</section>
	);
};

export default VerifyUpdates;
