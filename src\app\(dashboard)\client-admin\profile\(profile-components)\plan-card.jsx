'use client';
import React, { useState } from 'react';
import { ChevronLeftIcon, ChevronRightIcon, PlusIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { format, addDays, subDays } from 'date-fns';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

const PlanCard = ({
	plans = [
		{
			id: 1,
			fromTime: '09:00 AM',
			toTime: '10:30 AM',
			description:
				'Daily stand-up + sprint planning with the dev team to align on priorities and blockers.',
		},
		{
			id: 2,
			fromTime: '10:45 AM',
			toTime: '12:00 PM',
			description:
				'Work on authentication flow bugs, token expiry edge cases, and session handling improvements.',
		},
		{
			id: 3,
			fromTime: '12:15 PM',
			toTime: '01:30 PM',
			description:
				'Code review with backend team for PRs related to user service refactor and DB optimization logic.',
		},
	],
}) => {
	const [currentDate, setCurrentDate] = useState(new Date());

	const handlePrev = () => setCurrentDate((prev) => subDays(prev, 1));
	const handleNext = () => setCurrentDate((prev) => addDays(prev, 1));

	const isToday = (date) => {
		const today = new Date();
		return (
			date.getDate() === today.getDate() &&
			date.getMonth() === today.getMonth() &&
			date.getFullYear() === today.getFullYear()
		);
	};

	return (
		<div className="flex-1 bg-white shadow-sm border border-gray-200 rounded-xl p-3 space-y-3 text-sm min-h-[200px]">
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
				<h2 className="text-base font-semibold text-gray-800 flex items-center gap-2 h-8">
					Plan My Day
				</h2>
				<div className="h-8 flex items-center gap-2">
					<Button variant="outline" size="icon" onClick={handlePrev}>
						<ChevronLeftIcon />
					</Button>
					<div>
						<span className="text-sm text-gray-500">
							{isToday(currentDate)
								? 'Today'
								: format(currentDate, 'do MMM, yyyy')}
						</span>
					</div>
					<Button variant="outline" size="icon" onClick={handleNext}>
						<ChevronRightIcon />
					</Button>

					<Button
						variant="outline"
						size="icon"
						className="ml-2 bg-green-200 text-green-600"
					>
						<PlusIcon />
					</Button>
				</div>
			</div>

			<div className="max-h-[140px] overflow-y-auto space-y-2 border-t pt-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
				{plans.map((plan) => (
					<div className="flex ml-1 align-middle" key={plan.id}>
						<div className="left-0 my-1 w-1.5 h-auto rounded-full bg-red-500"></div>
						<Alert key={plan.id} className="pl-3 border-0 py-1">
							<AlertTitle className="font-semibold">
								{plan.fromTime} - {plan.toTime}
							</AlertTitle>
							<AlertDescription>{plan.description}</AlertDescription>
						</Alert>
					</div>
				))}
			</div>
		</div>
	);
};

export default PlanCard;
