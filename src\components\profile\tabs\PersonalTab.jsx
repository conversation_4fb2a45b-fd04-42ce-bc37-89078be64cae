import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { User, Pencil } from 'lucide-react';
import { formatDate } from '../utils/dateUtils';

export function PersonalTab({ personalDetails, onEditSection }) {
	return (
		<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
			<Card className="overflow-hidden">
				<CardHeader className="bg-slate-50">
					<div className="flex justify-between items-center">
						<div>
							<CardTitle className="flex items-center gap-2">
								<User className="h-5 w-5 text-indigo-600" />
								Personal Information
							</CardTitle>
							<CardDescription>
								Your personal information. Click edit to request changes.
							</CardDescription>
						</div>
						<Button
							variant="outline"
							size="sm"
							onClick={() => onEditSection('personal')}
							className="flex items-center self-start gap-2"
						>
							<Pencil className="h-4 w-4" />
							Edit Section
						</Button>
					</div>
				</CardHeader>
				<CardContent className="pt-6">
					<div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
						<div>
							<p className="text-sm text-muted-foreground">Full Name</p>
							<p className="font-medium">
								{personalDetails?.nameOnNRIC || 'N/A'}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Employee ID</p>
							<p className="font-medium">
								{personalDetails?.employeeOrgId || 'N/A'}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Date of Birth</p>
							<p className="font-medium">
								{formatDate(personalDetails?.dob)}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Gender</p>
							<p className="font-medium capitalize">
								{personalDetails?.gender || 'N/A'}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Nationality</p>
							<p className="font-medium capitalize">
								{personalDetails?.nationality || 'N/A'}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Religion</p>
							<p className="font-medium capitalize">
								{personalDetails?.religion || 'N/A'}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">NRIC/Passport</p>
							<p className="font-medium">
								{personalDetails?.nric || 'N/A'}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Mobile Number</p>
							<p className="font-medium">
								{personalDetails?.countryDialCode || ''}{' '}
								{personalDetails?.mobile || 'N/A'}
							</p>
						</div>
						<div>
							<p className="text-sm text-muted-foreground">Date of Joining</p>
							<p className="font-medium">
								{formatDate(personalDetails?.dateOfJoining)}
							</p>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
