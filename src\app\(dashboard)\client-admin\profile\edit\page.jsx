'use client';
import { ProfilePageComponent } from '@/components/profile-page-component';
import { fetchProfilePageDetails } from '@/lib/features/employees/employeeSlice';
import { useAppDispatch, useAppSelector } from '@/lib/hooks';
import { useEffect } from 'react';
export default function ProfilePage() {
	const dispatch = useAppDispatch();
	useEffect(() => {
		dispatch(fetchProfilePageDetails('680000e8a7fdfd08d17b9158'));
	}, [dispatch]);

	return (
		<div className="container mx-auto">
			<ProfilePageComponent />
		</div>
	);
}
